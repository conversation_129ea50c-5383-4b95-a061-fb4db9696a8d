{"name": "idb-keyval", "version": "6.2.2", "description": "A super-simple-small keyval store built on top of IndexedDB", "main": "./dist/compat.cjs", "module": "./dist/compat.js", "unpkg": "./dist/iife-compat.js", "exports": {".": {"types": "./dist/index.d.ts", "module": "./dist/index.js", "import": "./dist/index.js", "default": "./dist/index.cjs"}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "files": ["dist/**"], "type": "module", "types": "./dist/index.d.ts", "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/jakearchibald/idb-keyval.git"}, "keywords": ["idb", "indexeddb", "store", "keyval", "localstorage", "storage", "promise"], "author": "<PERSON>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/jakearchibald/idb-keyval/issues"}, "homepage": "https://github.com/jakearchibald/idb-keyval#readme", "devDependencies": {"@babel/core": "^7.27.1", "@babel/plugin-external-helpers": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/runtime": "^7.27.1", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/chai": "^5.2.2", "@types/mocha": "^10.0.10", "chai": "^5.2.0", "conditional-type-checks": "^1.0.6", "del": "^8.0.0", "filesize": "^10.1.6", "glob": "^11.0.2", "husky": "^9.1.7", "lint-staged": "^15.5.2", "mocha": "^11.2.2", "prettier": "^3.5.3", "rollup": "^4.40.2", "serve": "^14.2.4", "tslib": "^2.8.1", "typescript": "^5.8.3"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,css,md,ts,html}": "prettier --write"}, "scripts": {"build": "rollup -c && node lib/size-report.js", "dev": "rollup -cw & serve"}}