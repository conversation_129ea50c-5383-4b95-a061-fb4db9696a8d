# Copilot Instructions

- [x] Clarify Project Requirements
- [x] Scaffold the Project
- [ ] Customize the Project
- [ ] Install Required Extensions
- [ ] Compile the Project
- [ ] Create and Run Task
- [ ] Launch the Project
- [ ] Ensure Documentation is Complete

## Progress
- Project requirements clarified: Python project with src, tests, requirements.txt, README.md, and main.py entry point.
- Project scaffolded: folders created.
