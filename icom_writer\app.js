const Tesseract = require('tesseract.js');
const express = require('express');
const { exec } = require('child_process');
const path = require('path');
const https = require('https');
const fs = require('fs');
const app = express();
const port = 8080;
let server;
try {
  const sslOptions = {
    key: fs.readFileSync('server.key'),
    cert: fs.readFileSync('server.cert')
  };
  server = https.createServer(sslOptions, app);
} catch (e) {
  console.warn('SSL cert not found, falling back to HTTP:', e.message);
  server = app;
}

app.use(express.json({ limit: '10mb' }));

app.post('/write-ics205-csv', (req, res) => {
  const { channels } = req.body;
  if (!Array.isArray(channels) || !channels.length) {
    return res.status(400).json({ message: 'No channel data provided.' });
  }
  // Prepare CSV header (only relevant columns)
  const header = 'name,rx,rxtone,tx,txtone\n';
  const rows = channels.map(row =>
    `"${row.name}","${row.rx}","${row.rxtone}","${row.tx}","${row.txtone}"`
  ).join('\n');
  try {
    fs.writeFileSync('new_channel.csv', header + rows + '\n');
    res.json({ message: 'Channels written to new_channel.csv successfully.' });
  } catch (err) {
    res.status(500).json({ message: 'Failed to write CSV: ' + err.message });
  }
});

// OCR endpoint for ICS-205 form
app.post('/ocr-ics205', async (req, res) => {
  try {
    const { image } = req.body;
    // Remove data URL prefix
    const base64Data = image.replace(/^data:image\/png;base64,/, "");
    const buffer = Buffer.from(base64Data, 'base64');
    // Run OCR
    const result = await Tesseract.recognize(buffer, 'eng');
    const text = result.data.text;
    // Parse text into array of channel values
    // Only extract: name, rx freq, rx tone, tx freq, tx tone
    const lines = text.split('\n');
    const channelRows = [];
    for (const line of lines) {
      // Match lines with frequencies and tones
      if (/\d+\.\d+/.test(line)) {
        const parts = line.trim().split(/\s+/);
        // Attempt to find relevant columns by position
        // Example: [channel, name, rx freq, rx tone, tx freq, tx tone]
        let name = '', rx = '', rxtone = '', tx = '', txtone = '';
        // Find frequency and tone columns by regex
        for (let i = 0; i < parts.length; i++) {
          if (/\d+\.\d+/.test(parts[i]) && !rx) rx = parts[i];
          else if (/^(\d+\.\d+|OFF|none)$/i.test(parts[i]) && rx && !rxtone) rxtone = parts[i];
          else if (/\d+\.\d+/.test(parts[i]) && rx && rxtone && !tx) tx = parts[i];
          else if (/^(\d+\.\d+|OFF|none)$/i.test(parts[i]) && tx && !txtone) txtone = parts[i];
        }
        // Name is usually after channel number, before rx freq
        if (parts.length > 2) name = parts[1];
        channelRows.push({ name, rx, rxtone, tx, txtone });
      }
    }
    res.json({ text, channels: channelRows });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// ICS-205 Scan Page
app.get('/scan-ics205', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
      <title>Scan ICS-205 Form</title>
      <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
      <style>
        body {
            font-family: 'Orbitron', monospace;
            background: #1a1a1a;
            color: #00ffff;
            padding: 40px;
            text-align: center;
        }
        #video {
            width: 100%;
            max-width: 400px;
            border: 2px solid #00ffff;
            border-radius: 8px;
        }
        #canvas {
            display: none;
        }
        button {
            font-family: 'Orbitron', monospace;
            font-size: 18px;
            padding: 12px 24px;
            border-radius: 6px;
            border: none;
            background: #0066cc;
            color: white;
            margin: 20px;
            cursor: pointer;
        }
        button:hover {
            background: #0088ff;
        }

        /* Mobile optimizations for 385x845 resolution - ICS-205 Scan */
        @media (max-width: 400px) {
            body {
                padding: 12px;
                max-width: 385px;
                margin: 0 auto;
            }

            h1 {
                font-size: 26px;
                margin-bottom: 16px;
                line-height: 1.2;
            }

            #video {
                max-width: 100%;
                width: 360px;
                height: auto;
                border-radius: 6px;
            }

            button {
                font-size: 22px;
                padding: 20px 32px;
                margin: 12px 4px;
                min-height: 64px;
                width: auto;
                min-width: 220px;
                border-radius: 8px;
                font-weight: bold;
            }

            #ocr-result {
                font-size: 18px;
                margin-top: 16px;
                text-align: left;
                max-width: 100%;
                overflow-x: auto;
                padding: 8px;
            }

            #ocr-result pre {
                font-size: 16px;
                white-space: pre-wrap;
                word-wrap: break-word;
                padding: 12px;
                border-radius: 6px;
            }

            #ocr-result table {
                font-size: 16px;
                width: 100%;
                border-collapse: collapse;
                margin-top: 12px;
            }

            #ocr-result th,
            #ocr-result td {
                padding: 10px 6px;
                border: 1px solid #333;
                text-align: center;
                font-size: 14px;
            }

            #ocr-result h2 {
                font-size: 22px;
                margin: 16px 0 12px 0;
            }
        }
      </style>
    </head>
    <body>
      <h1>Scan ICS-205 Form</h1>
  <video id="video" autoplay playsinline style="background:#222;"></video>
      <canvas id="canvas"></canvas>
      <br>
      <button id="capture">Capture & OCR</button>
      <div id="ocr-result"></div>
      <script>
        (function() {
          const video = document.getElementById('video');
          const canvas = document.getElementById('canvas');
          const captureBtn = document.getElementById('capture');
          const ocrResult = document.getElementById('ocr-result');
          function showError(msg) {
            ocrResult.innerHTML = '<div style="color:#f00;">' + msg + '</div>';
          }
          // Prefer rear camera for document scanning
          const constraints = { video: { facingMode: { exact: 'environment' } } };
          navigator.mediaDevices.getUserMedia(constraints)
            .then(function(stream) {
              video.srcObject = stream;
            })
            .catch(function(err) {
              // Fallback to any camera if rear camera is not available
              navigator.mediaDevices.getUserMedia({ video: true })
                .then(function(stream) {
                  video.srcObject = stream;
                })
                .catch(function(err2) {
                  showError('Camera access failed: ' + err2.message);
                });
            });
          captureBtn.onclick = function() {
            if (!video.srcObject) {
              showError('No camera stream available.');
              return;
            }
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);
            // Image preprocessing: grayscale and contrast
            let imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            let data = imageData.data;
            // Convert to grayscale and boost contrast
            for (let i = 0; i < data.length; i += 4) {
              // Grayscale
              let avg = (data[i] + data[i+1] + data[i+2]) / 3;
              // Contrast boost
              avg = avg > 128 ? Math.min(255, avg * 1.2) : Math.max(0, avg * 0.8);
              data[i] = data[i+1] = data[i+2] = avg;
            }
            ctx.putImageData(imageData, 0, 0);
            var dataUrl = canvas.toDataURL('image/png');
            ocrResult.innerHTML = 'Processing...';
            fetch('/ocr-ics205', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ image: dataUrl })
            })
            .then(function(res) { return res.json(); })
            .then(function(data) {
              var html = '<h2>OCR Result</h2><pre>' + data.text + '</pre>';
              if (data.channels && data.channels.length) {
                html += '<h2>Parsed Channels</h2><table border="1" style="margin:auto;color:#00ffff;"><tr><th>Radio</th><th>Name</th><th>RX</th><th>TX</th><th>RX Tone</th><th>TX Tone</th></tr>';
                for (var i = 0; i < data.channels.length; i++) {
                  var row = data.channels[i];
                  html += '<tr><td>' + row.radio + '</td><td>' + row.name + '</td><td>' + row.rx + '</td><td>' + row.tx + '</td><td>' + row.rxtone + '</td><td>' + row.txtone + '</td></tr>';
                }
                html += '</table>';
                html += '<button id="confirm">Confirm & Write to CSV</button>';
              }
              ocrResult.innerHTML = html;
              if (data.channels && data.channels.length) {
                document.getElementById('confirm').onclick = function() {
                  fetch('/write-ics205-csv', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ channels: data.channels })
                  })
                  .then(function(res) { return res.json(); })
                  .then(function(resp) {
                    ocrResult.innerHTML += '<div style="color:#0f0;">' + resp.message + '</div>';
                  });
                };
              }
            });
          };
        })();
      </script>
    </body>
    </html>
  `);
});

// Load CTCSS tones from JSON file
let ctcssTones = ['OFF']
let workingDirectory = './'

// Load working directory from config first
try {
  const configData = fs.readFileSync('./radio_config.cfg', 'utf8')
  const lines = configData.split('\n')
  lines.forEach(line => {      
    const [key, value] = line.split('=')
    if (key && value && key.trim() === 'working_directory') {
      workingDirectory = value.trim()
    }
  })
} catch (error) {
  console.log('Config file not found, using default working directory')
}

try {
  const tonesData = fs.readFileSync(`${workingDirectory}vhf_ctts_tones.json`, 'utf8')
  const tones = JSON.parse(tonesData)
  ctcssTones = tones
} catch (error) {
  console.error('Error loading CTCSS tones:', error)
}

// Middleware to parse form data
app.use(express.urlencoded({ extended: true }))
app.use(express.json())

// Serve static files
app.use(express.static('public'))
app.use(express.static(workingDirectory))

// Main form route
app.get('/', (req, res) => {
  // Load current config
  let config = {
    radio1_enabled: 'enabled',
    radio1_band: 'vhf',
    radio2_enabled: 'disabled',
    radio2_band: 'uhf'
  }
  
  try {
    const configData = fs.readFileSync(`${workingDirectory}radio_config.cfg`, 'utf8')
    const lines = configData.split('\n')
    lines.forEach(line => {
      const [key, value] = line.split('=')
      if (key && value) {
        config[key.trim()] = value.trim()
      }
    })
  } catch (error) {
    console.log('Config file not found, using defaults')
  }

    // Password protection check
    if (config.password_protect === 'enabled') {
      // If not authenticated, show password form
      if (!req.query.auth || req.query.auth !== config.main_password) {
        return res.send(`
          <!DOCTYPE html>
          <html><head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
          <title>Password Required</title>
          <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
          <style>
          body {
              font-family: 'Orbitron', monospace;
              background: #1a1a1a;
              color: #00ffff;
              padding: 40px;
              text-align: center;
          }
          input[type='password'], button {
              font-family: 'Orbitron', monospace;
              font-size: 18px;
              padding: 12px;
              margin: 10px;
              border-radius: 6px;
              border: 2px solid #333;
              background: #2a2a2a;
              color: #00ffff;
          }
          button {
              background: #0066cc;
              color: white;
              cursor: pointer;
          }
          button:hover {
              background: #0088ff;
          }

          /* Mobile optimizations for 385x845 resolution - Password Page */
          @media (max-width: 400px) {
              body {
                  padding: 16px;
                  max-width: 385px;
                  margin: 0 auto;
              }

              h2 {
                  font-size: 24px;
                  margin-bottom: 18px;
                  line-height: 1.2;
              }

              input[type='password'] {
                  font-size: 22px;
                  padding: 20px;
                  margin: 6px;
                  width: 100%;
                  max-width: 340px;
                  min-height: 64px;
                  box-sizing: border-box;
                  border-radius: 6px;
              }

              button {
                  font-size: 22px;
                  padding: 20px 28px;
                  margin: 6px;
                  min-height: 64px;
                  width: 100%;
                  max-width: 340px;
                  box-sizing: border-box;
                  border-radius: 8px;
                  font-weight: bold;
              }
          }
          </style></head>
          <body>
          <h2>Password Protection Enabled</h2>
          <form method="GET" action="/">
            <input type="password" name="auth" placeholder="Enter password" required>
            <button type="submit">Access Main Page</button>
          </form>
          </body></html>
        `)
      }
    }

  // Generate options for tone dropdowns
  const toneOptions = ctcssTones.map(tone => 
    `<option value="${tone}" ${tone === 'OFF' ? 'selected' : ''}>${tone}</option>`
  ).join('')

  // Generate radio sections based on config
  const radio1Section = config.radio1_enabled === 'enabled' ? `
    <div class="radio-section">
        <h3>RADIO 1 - ${config.radio1_band.toUpperCase()}</h3>
        <div class="radio-row">
            <div class="form-group">
                <label for="radio1Name">Channel Name:</label>
                <input type="text" id="radio1Name" name="radio1Name" maxlength="14">
            </div>
        </div>
        <div class="radio-row">
            <div class="form-group">
                <label for="receiveFreq">RX Frequency (MHz):</label>
                <input type="number" id="receiveFreq" name="receiveFreq" step="0.001" min="136.000" max="512.000" required>
            </div>
            <div class="form-group">
                <label for="receiveTone">RX Tone (Hz):</label>
                <select id="receiveTone" name="receiveTone">
                    ${toneOptions}
                </select>
            </div>
        </div>
        <div class="radio-row">
            <div class="form-group">
                <label for="transmitFreq">TX Frequency (MHz):</label>
                <input type="number" id="transmitFreq" name="transmitFreq" step="0.001" min="136.000" max="512.000" required>
            </div>
            <div class="form-group">
                <label for="transmitTone">TX Tone (Hz):</label>
                <select id="transmitTone" name="transmitTone">
                    ${toneOptions}
                </select>
            </div>
        </div>
    </div>` : ''

  const radio2Section = config.radio2_enabled === 'enabled' ? `
    <div class="radio-section">
        <h3>RADIO 2 - ${config.radio2_band.toUpperCase()}</h3>
        <div class="radio-row">
            <div class="form-group">
                <label for="radio2Name">Channel Name:</label>
                <input type="text" id="radio2Name" name="radio2Name" maxlength="14">
            </div>
        </div>
        <div class="radio-row">
            <div class="form-group">
                <label for="radio2ReceiveFreq">RX Frequency (MHz):</label>
                <input type="number" id="radio2ReceiveFreq" name="radio2ReceiveFreq" step="0.001" min="136.000" max="512.000">
            </div>
            <div class="form-group">
                <label for="radio2ReceiveTone">RX Tone (Hz):</label>
                <select id="radio2ReceiveTone" name="radio2ReceiveTone">
                    ${toneOptions}
                </select>
            </div>
        </div>
        <div class="radio-row">
            <div class="form-group">
                <label for="radio2TransmitFreq">TX Frequency (MHz):</label>
                <input type="number" id="radio2TransmitFreq" name="radio2TransmitFreq" step="0.001" min="136.000" max="512.000">
            </div>
            <div class="form-group">
                <label for="radio2TransmitTone">TX Tone (Hz):</label>
                <select id="radio2TransmitTone" name="radio2TransmitTone">
                    ${toneOptions}
                </select>
            </div>
        </div>
    </div>` : ''

  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
        <title>ICOM Writer</title>
        <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
        <style>
            * {
                box-sizing: border-box;
            }
            body { 
                font-family: 'Orbitron', monospace; 
                background-color: #1a1a1a; 
                color: #00ffff; 
                margin: 0;
                padding: 15px; 
                font-size: 18px;
                min-height: 100vh;
                width: 100%;
            }
            h1 { 
                text-align: center; 
                color: #ffffff; 
                text-shadow: 0 0 10px #00ffff; 
                font-size: 32px;
                margin-bottom: 30px;
            }
            .form-section {
                margin-bottom: 25px;
                width: 100%;
            }
            .form-row { 
                display: flex; 
                gap: 15px; 
                margin-bottom: 20px; 
                width: 100%;
            }
            .form-group { 
                flex: 1; 
                width: 100%;
            }
            .radio-section {
                border: 2px solid #333;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 25px;
                background-color: #252525;
                width: 100%;
            }
            .radio-section.disabled {
                opacity: 0.5;
                background-color: #1a1a1a;
            }
            .radio-section h3 {
                margin: 0 0 20px 0;
                color: #ffffff;
                font-size: 22px;
                text-align: center;
                border-bottom: 1px solid #333;
                padding-bottom: 15px;
            }
            .radio-row {
                display: flex;
                gap: 15px;
                width: 100%;
            }
            label { 
                display: block; 
                margin-bottom: 10px; 
                font-weight: bold; 
                color: #ffffff; 
                font-size: 16px; 
            }
            input[type="text"], input[type="number"], select, textarea { 
                width: 100%; 
                padding: 18px; 
                border: 2px solid #333; 
                border-radius: 8px; 
                background-color: #2a2a2a; 
                color: #00ffff; 
                font-family: 'Orbitron', monospace; 
                font-size: 18px; 
                box-sizing: border-box; 
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                resize: vertical;
                min-height: 56px;
            }
            select {
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%2300ffff' viewBox='0 0 16 16'%3e%3cpath d='M8 13.1l4.7-4.7-1.4-1.4L8 10.3 4.7 7l-1.4 1.4z'/%3e%3c/svg%3e");
                background-repeat: no-repeat;
                background-position: right 18px center;
                background-size: 20px;
                padding-right: 50px;
            }
            input[type="text"]:focus, input[type="number"]:focus, select:focus, textarea:focus { 
                outline: none; 
                border-color: #00ffff; 
                box-shadow: 0 0 12px #00ffff; 
            }
            button { 
                background-color: #0066cc; 
                color: white; 
                padding: 22px; 
                border: none; 
                border-radius: 8px; 
                cursor: pointer; 
                font-family: 'Orbitron', monospace; 
                font-size: 20px; 
                font-weight: bold; 
                width: 100%; 
                margin-top: 30px; 
                touch-action: manipulation;
                min-height: 60px;
            }
            button:hover, button:active { 
                background-color: #0088ff; 
                box-shadow: 0 0 15px #0088ff; 
            }
            
            .copyright {
                text-align: center;
                font-size: 10px;
                color: #666;
                margin-top: 30px;
                padding: 10px;
            }
            
            /* Mobile optimizations for 385x845 resolution */
            @media (max-width: 400px) {
                body {
                    padding: 6px;
                    font-size: 22px;
                    max-width: 385px;
                    margin: 0 auto;
                }

                h1 {
                    font-size: 30px;
                    margin-bottom: 18px;
                    line-height: 1.2;
                }

                .radio-section {
                    padding: 12px;
                    margin-bottom: 16px;
                    border-radius: 6px;
                }

                .radio-section h3 {
                    font-size: 26px;
                    margin-bottom: 12px;
                    padding-bottom: 8px;
                    line-height: 1.1;
                }

                .radio-row {
                    flex-direction: column;
                    gap: 10px;
                }

                .form-row {
                    flex-direction: column;
                    gap: 0;
                }

                label {
                    font-size: 20px;
                    margin-bottom: 6px;
                    font-weight: bold;
                }

                input[type="text"] {
                    font-size: 22px;
                    padding: 20px;
                    min-height: 68px;
                    border-radius: 6px;
                }

                input[type="number"], select {
                    font-size: 26px;
                    padding: 22px;
                    min-height: 76px;
                    border-radius: 6px;
                }

                select {
                    padding-right: 55px;
                    background-size: 26px;
                    background-position: right 20px center;
                }

                textarea {
                    font-size: 22px;
                    padding: 20px;
                    min-height: 100px;
                    border-radius: 6px;
                }

                button {
                    padding: 26px;
                    font-size: 26px;
                    min-height: 76px;
                    margin-top: 20px;
                    border-radius: 8px;
                    font-weight: bold;
                }

                .copyright {
                    font-size: 14px;
                    margin-top: 16px;
                    padding: 8px;
                }
            }

            /* Fallback for larger mobile screens */
            @media (min-width: 401px) and (max-width: 768px) {
                body {
                    padding: 10px;
                }
                .radio-row {
                    flex-direction: column;
                    gap: 15px;
                }
                .form-row {
                    flex-direction: column;
                    gap: 0;
                }
                textarea {
                    font-size: 72px;
                    padding: 20px;
                    min-height: 120px;
                }
                input[type="number"], select {
                    font-size: 36px;
                    padding: 20px;
                    min-height: 80px;
                }
                input[type="text"] {
                    font-size: 18px;
                    padding: 20px;
                    min-height: 60px;
                }
                select {
                    padding-right: 55px;
                }
                button {
                    padding: 25px;
                    font-size: 22px;
                    min-height: 65px;
                }
            }
        </style>
    </head>
    <body>
        <h1>ICOM Radio Configuration</h1>
        <form action="/submit" method="POST">
            ${radio1Section}
            ${radio2Section}
            
            <button type="submit">Save Configuration</button>
        </form>
        <div class="copyright">© BlackHawk Tactical Imaging - 2025</div>
    </body>
    </html>
  `)
})

// Handle form submission
app.post('/submit', (req, res) => {
  const channels = []
  
  // Add Radio 1 channel if enabled and has data
  if (req.body.receiveFreq && req.body.transmitFreq) {
    channels.push({
      radio: 'radio1',
      name: req.body.radio1Name || req.body.name || 'Channel1',

      receiveFreq: req.body.receiveFreq,
      receiveTone: req.body.receiveTone,
      transmitFreq: req.body.transmitFreq,
      transmitTone: req.body.transmitTone
    })
  }
  
  // Add Radio 2 channel if enabled and has data
  if (req.body.radio2ReceiveFreq && req.body.radio2TransmitFreq) {
    channels.push({
      radio: 'radio2',
      name: req.body.radio2Name || 'Channel2',
      receiveFreq: req.body.radio2ReceiveFreq,
      receiveTone: req.body.radio2ReceiveTone,
      transmitFreq: req.body.radio2TransmitFreq,
      transmitTone: req.body.radio2TransmitTone
    })
  }
  
  console.log('Radio configurations saved:', JSON.stringify(channels, null, 2))
  
  // Create CSV content with radio column
  const csvHeader = '"radio","name","rx","tx","rxtone","txtone"\n'
  const csvRows = channels.map(channel => 
    `"${channel.radio}","${channel.name}","${channel.receiveFreq}","${channel.transmitFreq}","${channel.receiveTone}","${channel.transmitTone}"`
  ).join('\n')
  
  const csvContent = csvHeader + csvRows + '\n'
  
  // Write CSV file
  const csvPath = `${workingDirectory}new_channel.csv`
  try {
    fs.writeFileSync(csvPath, csvContent)
    console.log('CSV file written to:', csvPath)
    
    // Load config to check dev mode
    let devMode = false
    try {
      const configData = fs.readFileSync(`${workingDirectory}radio_config.cfg`, 'utf8')
      const lines = configData.split('\n')
      lines.forEach(line => {
        const [key, value] = line.split('=')
        if (key && value && key.trim() === 'dev_mode') {
          devMode = value.trim() === 'enabled'
        }
      })
    } catch (error) {
      console.log('Could not read config for dev mode check')
    }
    
    // Choose script based on dev mode
    const pythonScript = devMode 
      ? `python "${workingDirectory}v3.17.py"`
      : `"${workingDirectory}v3.17.exe"`
    
    console.log(`${devMode ? 'DEV MODE' : 'PRODUCTION MODE'}: Executing ${pythonScript}`)
    
    exec(pythonScript, (error, stdout, stderr) => {
      if (error) {
        console.error('Error executing script:', error)
      } else {
        console.log('Script executed successfully')
        if (stdout) console.log('stdout:', stdout)
        if (stderr) console.log('stderr:', stderr)
      }
    })
    
  } catch (error) {
    console.error('Error writing CSV file:', error)
  }
  
  res.send(`
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
    <style>
      body {
          font-family: 'Orbitron', monospace;
          background-color: #1a1a1a;
          color: #00ffff;
          padding: 20px;
      }
      h1 {
          color: #ffffff;
          text-shadow: 0 0 10px #00ffff;
      }
      pre {
          background-color: #2a2a2a;
          padding: 20px;
          border-radius: 4px;
          border: 2px solid #333;
      }
      a {
          color: #00ffff;
          text-decoration: none;
      }
      a:hover {
          text-shadow: 0 0 5px #00ffff;
      }

      /* Mobile optimizations for 385x845 resolution - Success Page */
      @media (max-width: 400px) {
          body {
              padding: 12px;
              font-size: 18px;
              max-width: 385px;
              margin: 0 auto;
          }

          h1 {
              font-size: 24px;
              margin-bottom: 16px;
              text-align: center;
              line-height: 1.2;
          }

          pre {
              padding: 16px;
              font-size: 16px;
              overflow-x: auto;
              white-space: pre-wrap;
              word-wrap: break-word;
              border-radius: 6px;
          }

          a {
              font-size: 20px;
              display: block;
              text-align: center;
              margin-top: 16px;
              padding: 18px;
              background-color: #0066cc;
              border-radius: 8px;
              color: white;
              text-decoration: none;
              font-weight: bold;
              min-height: 56px;
              line-height: 1.2;
          }

          a:hover {
              background-color: #0088ff;
              text-shadow: none;
              box-shadow: 0 0 10px #0088ff;
          }
      }
    </style>
    <h1>Configuration Applied to Radio!</h1>
    <pre>${(() => {
      let config = {};
      try {
        const configData = fs.readFileSync(`${workingDirectory}radio_config.cfg`, 'utf8');
        const lines = configData.split('\n');
        lines.forEach(line => {
          const [key, value] = line.split('=');
          if (key && value) config[key.trim()] = value.trim();
        });
      } catch (error) {
        config = { error: 'Config file not found' };
      }
      return JSON.stringify(config, null, 2);
    })()}</pre>
    <a href="/">Add Another Configuration</a>
  `)
})

// Manual QR generation for testing
app.get('/test-qr', (req, res) => {
  const command = `python qr_code_generator.py --config radio_config.cfg --outdir ./`
  console.log('Testing QR generation with command:', command)
  
  exec(command, { cwd: workingDirectory }, (error, stdout, stderr) => {
    if (error) {
      console.error('Error:', error)
      res.send(`Error: ${error.message}<br>stderr: ${stderr}`)
    } else {
      console.log('Success:', stdout)
      res.send(`Success!<br>stdout: ${stdout}<br>stderr: ${stderr}`)
    }
  })
})

// Check QR files route
app.get('/check-qr', (req, res) => {
  const wifiPath = path.join(workingDirectory, 'wifi.png')
  const sitePath = path.join(workingDirectory, 'local_site.png')
  
  res.json({
    wifi_exists: fs.existsSync(wifiPath),
    site_exists: fs.existsSync(sitePath),
    wifi_path: wifiPath,
    site_path: sitePath,
    working_directory: workingDirectory
  })
})

// Generate QR codes route
app.get('/generate-qr', (req, res) => {
  const pythonScript = path.join(workingDirectory, 'qr_code_generator.py')
  const configFile = path.join(workingDirectory, 'radio_config.cfg')
  
  exec(`python "${pythonScript}" --config "${configFile}" --outdir "${workingDirectory}"`, (error, stdout, stderr) => {
    if (error) {
      console.error('Error generating QR codes:', error)
      res.json({ success: false, error: error.message })
    } else {
      console.log('QR codes generated successfully')
      console.log('stdout:', stdout)
      res.json({ success: true, message: 'QR codes generated' })
    }
  })
})

// Admin login route
app.get('/admin', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
        <title>ICOM Writer - Admin</title>
        <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
        <style>
            body { 
                font-family: 'Orbitron', monospace; 
                background-color: #1a1a1a; 
                color: #00ffff; 
                margin: 0;
                padding: 20px; 
                min-height: 100vh;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .login-container {
                background-color: #252525;
                border: 2px solid #333;
                border-radius: 8px;
                padding: 40px;
                max-width: 400px;
                width: 100%;
            }
            h1 { 
                text-align: center; 
                color: #ffffff; 
                text-shadow: 0 0 10px #00ffff; 
                margin-bottom: 30px;
            }
            label { 
                display: block; 
                margin-bottom: 10px; 
                font-weight: bold; 
                color: #ffffff; 
            }
            input[type="password"] { 
                width: 100%; 
                padding: 15px; 
                border: 2px solid #333; 
                border-radius: 6px; 
                background-color: #2a2a2a; 
                color: #00ffff; 
                font-family: 'Orbitron', monospace; 
                font-size: 16px; 
                box-sizing: border-box; 
                margin-bottom: 20px;
                min-height: 48px;
            }
            input[type="password"]:focus { 
                outline: none; 
                border-color: #00ffff; 
                box-shadow: 0 0 8px #00ffff; 
            }
            button { 
                background-color: #0066cc; 
                color: white; 
                padding: 15px; 
                border: none; 
                border-radius: 6px; 
                cursor: pointer; 
                font-family: 'Orbitron', monospace; 
                font-size: 16px; 
                font-weight: bold; 
                width: 100%; 
            }
            button:hover {
                background-color: #0088ff;
                box-shadow: 0 0 10px #0088ff;
            }

            /* Mobile optimizations for 385x845 resolution - Admin Login */
            @media (max-width: 400px) {
                body {
                    padding: 12px;
                    max-width: 385px;
                    margin: 0 auto;
                }

                .login-container {
                    padding: 20px;
                    max-width: 100%;
                    border-radius: 6px;
                }

                h1 {
                    font-size: 24px;
                    margin-bottom: 20px;
                    line-height: 1.2;
                }

                label {
                    font-size: 18px;
                    margin-bottom: 6px;
                    font-weight: bold;
                }

                input[type="password"] {
                    font-size: 20px;
                    padding: 20px;
                    min-height: 64px;
                    margin-bottom: 16px;
                    border-radius: 6px;
                }

                button {
                    font-size: 20px;
                    padding: 20px;
                    min-height: 64px;
                    border-radius: 8px;
                    font-weight: bold;
                }
            }
        </style>
    </head>
    <body>
        <div class="login-container">
            <h1>Admin Login</h1>
            <form action="/admin/login" method="POST">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
                <button type="submit">Login</button>
            </form>
        </div>
    </body>
    </html>
  `)
})

// Admin login handler
app.post('/admin/login', (req, res) => {
  const password = req.body.password
  
  if (password === 'Aaron111!!!') {
    // Load current config
    let config = {
      radio1_enabled: 'disabled',
      radio1_band: 'vhf',
      radio1_comport: '1',
      radio2_enabled: 'disabled',
      radio2_band: 'uhf',
      radio2_comport: '2',
      wifi_ssid: '',
      wifi_password: '',
      dev_mode: 'disabled',
      working_directory: workingDirectory
    }
    
    try {
      const configData = fs.readFileSync(`${workingDirectory}radio_config.cfg`, 'utf8')
      const lines = configData.split('\n')
      lines.forEach(line => {
        const [key, value] = line.split('=')
        if (key && value) {
          config[key.trim()] = value.trim()
        }
      })
    } catch (error) {
      console.log('Config file not found, using defaults')
    }
    
    console.log('Admin login successful, config loaded:', JSON.stringify(config, null, 2))
    
    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
          <title>ICOM Writer - Admin Panel</title>
          <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
          <style>
              body { 
                  font-family: 'Orbitron', monospace; 
                  background-color: #1a1a1a; 
                  color: #00ffff; 
                  margin: 0;
                  padding: 20px; 
              }
              h1 { 
                  text-align: center; 
                  color: #ffffff; 
                  text-shadow: 0 0 10px #00ffff; 
                  margin-bottom: 30px;
              }
              .radio-section {
                  border: 2px solid #333;
                  border-radius: 8px;
                  padding: 20px;
                  margin-bottom: 25px;
                  background-color: #252525;
              }
              .radio-section.disabled {
                  opacity: 0.5;
                  background-color: #1a1a1a;
              }
              .radio-section h3 {
                  margin: 0 0 20px 0;
                  color: #ffffff;
                  font-size: 20px;
                  text-align: center;
                  border-bottom: 1px solid #333;
                  padding-bottom: 15px;
              }
              .form-row {
                  display: flex;
                  gap: 20px;
                  margin-bottom: 15px;
              }
              .form-group {
                  flex: 1;
              }
              label { 
                  display: block; 
                  margin-bottom: 8px; 
                  font-weight: bold; 
                  color: #ffffff; 
                  font-size: 14px;
              }
              select { 
                  width: 100%; 
                  padding: 12px; 
                  border: 2px solid #333; 
                  border-radius: 6px; 
                  background-color: #2a2a2a; 
                  color: #00ffff; 
                  font-family: 'Orbitron', monospace; 
                  font-size: 16px; 
                  box-sizing: border-box;
                  min-height: 48px;
              }
              select:focus { 
                  outline: none; 
                  border-color: #00ffff; 
                  box-shadow: 0 0 8px #00ffff; 
              }
              button { 
                  background-color: #0066cc; 
                  color: white; 
                  padding: 18px; 
                  border: none; 
                  border-radius: 8px; 
                  cursor: pointer; 
                  font-family: 'Orbitron', monospace; 
                  font-size: 18px; 
                  font-weight: bold; 
                  width: 100%; 
                  margin-top: 25px; 
              }
              button:hover { 
                  background-color: #0088ff; 
                  box-shadow: 0 0 15px #0088ff; 
              }
              .back-link {
                  display: block;
                  text-align: center;
                  margin-top: 20px;
                  color: #00ffff;
                  text-decoration: none;
              }
              .back-link:hover {
                  text-shadow: 0 0 5px #00ffff;
              }
              .copyright {
                  text-align: center;
                  font-size: 10px;
                  color: #666;
                  margin-top: 30px;
                  padding: 10px;
              }
              .qr-section {
                  display: flex;
                  justify-content: center;
                  gap: 40px;
                  margin: 30px 0;
                  flex-wrap: wrap;
              }
              .qr-item {
                  text-align: center;
              }
              .qr-item img {
                  width: 120px;
                  height: 120px;
                  border: 2px solid #333;
                  border-radius: 8px;
                  background-color: #fff;
                  padding: 10px;
              }
              .qr-item label {
                  display: block;
                  margin-top: 10px;
                  font-size: 14px;
                  color: #00ffff;
              }

              /* Mobile optimizations for 385x845 resolution - Admin Panel */
              @media (max-width: 400px) {
                  body {
                      padding: 8px;
                      max-width: 385px;
                      margin: 0 auto;
                  }

                  h1 {
                      font-size: 26px;
                      margin-bottom: 16px;
                      line-height: 1.2;
                  }

                  .radio-section {
                      padding: 12px;
                      margin-bottom: 16px;
                      border-radius: 6px;
                  }

                  .radio-section h3 {
                      font-size: 22px;
                      margin-bottom: 12px;
                      padding-bottom: 8px;
                      line-height: 1.1;
                  }

                  .form-row {
                      flex-direction: column;
                      gap: 10px;
                      margin-bottom: 10px;
                  }

                  label {
                      font-size: 18px;
                      margin-bottom: 5px;
                      font-weight: bold;
                  }

                  select, input[type="text"] {
                      font-size: 20px;
                      padding: 20px;
                      min-height: 60px;
                      border-radius: 6px;
                  }

                  button {
                      font-size: 22px;
                      padding: 24px;
                      margin-top: 16px;
                      border-radius: 8px;
                      min-height: 68px;
                  }

                  .qr-section {
                      flex-direction: column;
                      gap: 16px;
                      align-items: center;
                      margin: 20px 0;
                  }

                  .qr-item img {
                      width: 120px;
                      height: 120px;
                      padding: 6px;
                      border-radius: 6px;
                  }

                  .qr-item label {
                      font-size: 18px;
                      margin-top: 8px;
                  }

                  .qr-item button {
                      padding: 16px 24px;
                      font-size: 18px;
                      border-radius: 6px;
                  }

                  .back-link {
                      font-size: 20px;
                      margin-top: 16px;
                      padding: 12px;
                      display: block;
                      text-align: center;
                  }

                  .copyright {
                      font-size: 14px;
                      margin-top: 16px;
                      padding: 8px;
                  }
              }
          </style>
      </head>
      <body>
          <h1>Radio Configuration Admin</h1>
          <form action="/admin/save" method="POST">
        <div class="radio-section">
          <h3>PASSWORD PROTECTION</h3>
          <div class="form-row">
            <div class="form-group">
              <label for="password_protect" style="display: flex; align-items: center; cursor: pointer;">
                <input type="checkbox" id="password_protect" name="password_protect" value="enabled" ${config.password_protect === 'enabled' ? 'checked' : ''} style="margin-right: 10px; transform: scale(1.5);">
                Enable password protection
              </label>
            </div>
            <div class="form-group">
              <label for="main_password">Main Page Password:</label>
              <input type="text" id="main_password" name="main_password" value="${config.main_password || ''}" style="width: 100%; padding: 12px; border: 2px solid #333; border-radius: 6px; background-color: #2a2a2a; color: #00ffff; font-family: 'Orbitron', monospace; font-size: 16px; box-sizing: border-box; min-height: 48px;">
            </divnpm >
          </div>
        </div>
              <div class="radio-section${config.radio1_enabled === 'disabled' ? ' disabled' : ''}">
                  <h3>RADIO 1</h3>
                  <div class="form-row">
                      <div class="form-group">
                          <label for="radio1_enabled">Status:</label>
                          <select id="radio1_enabled" name="radio1_enabled">
                              <option value="enabled" ${config.radio1_enabled === 'enabled' ? 'selected' : ''}>Enabled</option>
                              <option value="disabled" ${config.radio1_enabled === 'disabled' ? 'selected' : ''}>Disabled</option>
                          </select>
                      </div>
                      <div class="form-group">
                          <label for="radio1_band">Band:</label>
                          <select id="radio1_band" name="radio1_band">
                              <option value="vhf" ${config.radio1_band === 'vhf' ? 'selected' : ''}>VHF</option>
                              <option value="uhf" ${config.radio1_band === 'uhf' ? 'selected' : ''}>UHF</option>
                          </select>
                      </div>
                      <div class="form-group">
                          <label for="radio1_comport">COM Port:</label>
                          <select id="radio1_comport" name="radio1_comport">
                              ${Array.from({length: 20}, (_, i) => i + 1).map(port => 
                                `<option value="${port}" ${config.radio1_comport === port.toString() ? 'selected' : ''}>${port}</option>`
                              ).join('')}
                          </select>
                      </div>
                  </div>
              </div>
              
              <div class="radio-section">
                  <h3>RADIO 2</h3>
                  <div class="form-row">
                      <div class="form-group">
                          <label for="radio2_enabled">Status:</label>
                          <select id="radio2_enabled" name="radio2_enabled">
                              <option value="enabled" ${config.radio2_enabled === 'enabled' ? 'selected' : ''}>Enabled</option>
                              <option value="disabled" ${config.radio2_enabled === 'disabled' ? 'selected' : ''}>Disabled</option>
                          </select>
                      </div>
                      <div class="form-group">
                          <label for="radio2_band">Band:</label>
                          <select id="radio2_band" name="radio2_band">
                              <option value="vhf" ${config.radio2_band === 'vhf' ? 'selected' : ''}>VHF</option>
                              <option value="uhf" ${config.radio2_band === 'uhf' ? 'selected' : ''}>UHF</option>
                          </select>
                      </div>
                      <div class="form-group">
                          <label for="radio2_comport">COM Port:</label>
                          <select id="radio2_comport" name="radio2_comport">
                              ${Array.from({length: 20}, (_, i) => i + 1).map(port => 
                                `<option value="${port}" ${config.radio2_comport === port.toString() ? 'selected' : ''}>${port}</option>`
                              ).join('')}
                          </select>
                      </div>
                  </div>
              </div>
              
              <div class="radio-section">
                  <h3>WORKING DIRECTORY</h3>
                  <div class="form-row">
                      <div class="form-group">
                          <label for="working_directory">Current Directory:</label>
                          <input type="text" id="working_directory" name="working_directory" value="${workingDirectory}" readonly style="width: 100%; padding: 12px; border: 2px solid #333; border-radius: 6px; background-color: #1a1a1a; color: #666; font-family: 'Orbitron', monospace; font-size: 16px; box-sizing: border-box; min-height: 48px; opacity: 0.7;">
                      </div>
                  </div>
              </div>
              
              <div class="radio-section">
                  <h3>WIFI CONFIGURATION</h3>
                  <div class="form-row">
                      <div class="form-group">
                          <label for="wifi_ssid">WiFi SSID:</label>
                          <input type="text" id="wifi_ssid" name="wifi_ssid" value="${config.wifi_ssid}" style="width: 100%; padding: 12px; border: 2px solid #333; border-radius: 6px; background-color: #2a2a2a; color: #00ffff; font-family: 'Orbitron', monospace; font-size: 16px; box-sizing: border-box; min-height: 48px;">
                      </div>
                      <div class="form-group">
                          <label for="wifi_password">WiFi Password:</label>
                          <input type="text" id="wifi_password" name="wifi_password" value="${config.wifi_password}" style="width: 100%; padding: 12px; border: 2px solid #333; border-radius: 6px; background-color: #2a2a2a; color: #00ffff; font-family: 'Orbitron', monospace; font-size: 16px; box-sizing: border-box; min-height: 48px;">
                      </div>
                  </div>
              </div>
              
              <div class="radio-section">
                  <h3>DEVELOPER OPTIONS</h3>
                  <div class="form-row">
                      <div class="form-group">
                          <label for="dev_mode" style="display: flex; align-items: center; cursor: pointer;">
                              <input type="checkbox" id="dev_mode" name="dev_mode" value="enabled" ${config.dev_mode === 'enabled' ? 'checked' : ''} style="margin-right: 10px; transform: scale(1.5);">
                              Enable Developer Mode
                          </label>
                          <small style="color: #666; font-size: 12px; margin-top: 5px; display: block;">Enables debug logging and additional features</small>
                      </div>
                  </div>
              </div>
              
              <button type="submit">Save Configuration</button>
          </form>
          
          <div class="radio-section">
              <h3>QR CODE MANAGEMENT</h3>
              <div class="qr-section">
                  <div class="qr-item">
                      <div id="wifi-qr-container">
                          <img id="wifi-qr" src="/wifi.png" alt="WiFi QR Code" style="width: 120px; height: 120px; border: 2px solid #333; border-radius: 8px; background-color: #fff; padding: 10px;" onload="this.style.display='block'; this.nextElementSibling.style.display='none';" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                          <div style="display:block; padding:20px; border:2px solid #333; background:#2a2a2a; color:#666; width: 120px; height: 120px; text-align: center; line-height: 80px;">No QR Code</div>
                      </div>
                      <label>WiFi Connection</label>
                  </div>
                  <div class="qr-item">
                      <div id="site-qr-container">
                          <img id="site-qr" src="/local_site.png" alt="Radio Programming QR Code" style="width: 120px; height: 120px; border: 2px solid #333; border-radius: 8px; background-color: #fff; padding: 10px;" onload="this.style.display='block'; this.nextElementSibling.style.display='none';" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                          <div style="display:block; padding:20px; border:2px solid #333; background:#2a2a2a; color:#666; width: 120px; height: 120px; text-align: center; line-height: 80px;">No QR Code</div>
                      </div>
                      <label>Radio Programming</label>
                  </div>
                  <div class="qr-item">
                      <button type="button" id="generate-btn" style="padding:15px 25px; background:#0066cc; color:white; border:none; border-radius:4px; cursor:pointer; font-family: 'Orbitron', monospace;">Generate QR Codes</button>
                      <div id="status-message" style="margin-top: 10px; font-size: 14px; color: #00ffff;"></div>
                  </div>
              </div>
          </div>
          
          <script>
          document.getElementById('generate-btn').addEventListener('click', function() {
              const btn = this;
              const status = document.getElementById('status-message');
              
              btn.disabled = true;
              btn.textContent = 'Generating...';
              status.textContent = 'Creating QR codes...';
              
              fetch('/generate-qr')
                  .then(response => {
                      console.log('Response status:', response.status);
                      return response.json();
                  })
                  .then(data => {
                      console.log('Response data:', data);
                      if (data.success) {
                          status.textContent = 'QR codes generated successfully!';
                          status.style.color = '#00ff00';
                          
                          // Force reload images with new timestamp
                          const timestamp = Date.now();
                          document.getElementById('wifi-qr').src = 'wifi.png?t=' + timestamp;
                          document.getElementById('site-qr').src = 'local_site.png?t=' + timestamp;
                          
                          setTimeout(() => {
                              status.textContent = '';
                              status.style.color = '#00ffff';
                          }, 3000);
                      } else {
                          status.textContent = 'Error: ' + (data.error || 'Unknown error');
                          status.style.color = '#ff0000';
                      }
                  })
                  .catch(error => {
                      console.error('Fetch error:', error);
                      status.textContent = 'Network error: ' + error.message;
                      status.style.color = '#ff0000';
                  })
                  .finally(() => {
                      btn.disabled = false;
                      btn.textContent = 'Generate QR Codes';
                  });
          });
          </script>
          
          <a href="/" class="back-link">← Back to Main</a>
          <div class="copyright">© BlackHawk Tactical Imaging - 2025</div>
      </body>
      </html>
    `)
  } else {
    res.send(`
      <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
      <style>
        body {
            font-family: 'Orbitron', monospace;
            background-color: #1a1a1a;
            color: #ff0000;
            padding: 20px;
            text-align: center;
        }
        a {
            color: #00ffff;
            text-decoration: none;
        }
        a:hover {
            text-shadow: 0 0 5px #00ffff;
        }

        /* Mobile optimizations for 385x845 resolution - Error Pages */
        @media (max-width: 400px) {
            body {
                padding: 12px;
                font-size: 18px;
                max-width: 385px;
                margin: 0 auto;
            }

            h1 {
                font-size: 24px;
                margin-bottom: 16px;
                line-height: 1.2;
            }

            p {
                font-size: 20px;
                margin-bottom: 16px;
                line-height: 1.3;
            }

            a {
                font-size: 20px;
                display: inline-block;
                padding: 18px 28px;
                background-color: #0066cc;
                border-radius: 8px;
                color: white;
                text-decoration: none;
                margin-top: 16px;
                font-weight: bold;
                min-height: 56px;
                line-height: 1.2;
            }

            a:hover {
                background-color: #0088ff;
                text-shadow: none;
                box-shadow: 0 0 10px #0088ff;
            }
        }
      </style>
      <h1>Access Denied</h1>
      <p>Invalid password</p>
      <a href="/admin">Try Again</a>
    `)
  }
})

// Admin save handler
app.post('/admin/save', (req, res) => {
  const config = {
    radio1_enabled: req.body.radio1_enabled,
    radio1_band: req.body.radio1_band,
    radio1_comport: req.body.radio1_comport,
    radio2_enabled: req.body.radio2_enabled,
    radio2_band: req.body.radio2_band,
    radio2_comport: req.body.radio2_comport,
    wifi_ssid: req.body.wifi_ssid,
    wifi_password: req.body.wifi_password,
    dev_mode: req.body.dev_mode || 'disabled',
    working_directory: workingDirectory,
    password_protect: req.body.password_protect === 'enabled' ? 'enabled' : 'disabled',
    main_password: req.body.main_password || ''
  }
  
  // Create config file content
  
const configContent = Object.entries(config)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n')
  
  // Write config file
  const configPath = `${workingDirectory}radio_config.cfg`
  try {
    fs.writeFileSync(configPath, configContent)
    console.log('Config file saved successfully:', JSON.stringify(config, null, 2))
    
    res.send(`
      <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
      <style>
        body {
            font-family: 'Orbitron', monospace;
            background-color: #1a1a1a;
            color: #00ffff;
            padding: 20px;
        }
        h1 {
            color: #ffffff;
            text-shadow: 0 0 10px #00ffff;
        }
        pre {
            background-color: #2a2a2a;
            padding: 20px;
            border-radius: 4px;
            border: 2px solid #333;
        }
        a {
            color: #00ffff;
            text-decoration: none;
            margin-right: 20px;
        }
        a:hover {
            text-shadow: 0 0 5px #00ffff;
        }

        /* Mobile optimizations for 385x845 resolution - Admin Save Results */
        @media (max-width: 400px) {
            body {
                padding: 12px;
                font-size: 18px;
                max-width: 385px;
                margin: 0 auto;
            }

            h1 {
                font-size: 24px;
                margin-bottom: 16px;
                text-align: center;
                line-height: 1.2;
            }

            pre {
                padding: 16px;
                font-size: 16px;
                overflow-x: auto;
                white-space: pre-wrap;
                word-wrap: break-word;
                border-radius: 6px;
            }

            a {
                font-size: 20px;
                display: block;
                text-align: center;
                margin: 8px 0;
                padding: 18px;
                background-color: #0066cc;
                border-radius: 8px;
                color: white;
                text-decoration: none;
                font-weight: bold;
                min-height: 56px;
                line-height: 1.2;
            }

            a:hover {
                background-color: #0088ff;
                text-shadow: none;
                box-shadow: 0 0 10px #0088ff;
            }
        }
      </style>
      <h1>Configuration Saved!</h1>
      <pre>${JSON.stringify(config, null, 2)}</pre>
      <a href="/admin">Back to Admin</a>
      <a href="/">Back to Main</a>
    `)
  } catch (error) {
    console.error('Error writing config file:', error)
    res.send(`
      <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
      <style>
        body {
            font-family: 'Orbitron', monospace;
            background-color: #1a1a1a;
            color: #ff0000;
            padding: 20px;
        }
        a {
            color: #00ffff;
            text-decoration: none;
        }
        a:hover {
            text-shadow: 0 0 5px #00ffff;
        }

        /* Mobile optimizations for 385x845 resolution - Admin Error Page */
        @media (max-width: 400px) {
            body {
                padding: 12px;
                font-size: 18px;
                text-align: center;
                max-width: 385px;
                margin: 0 auto;
            }

            h1 {
                font-size: 24px;
                margin-bottom: 16px;
                line-height: 1.2;
            }

            p {
                font-size: 20px;
                margin-bottom: 16px;
                word-wrap: break-word;
                line-height: 1.3;
            }

            a {
                font-size: 20px;
                display: inline-block;
                padding: 18px 28px;
                background-color: #0066cc;
                border-radius: 8px;
                color: white;
                text-decoration: none;
                margin: 8px 4px;
                font-weight: bold;
                min-height: 56px;
                line-height: 1.2;
            }

            a:hover {
                background-color: #0088ff;
                text-shadow: none;
                box-shadow: 0 0 10px #0088ff;
            }
        }
      </style>
      <h1>Error Saving Configuration</h1>
      <p>${error.message}</p>
      <a href="/admin">Back to Admin</a>
    `)
  }
})

if (server === app) {
  app.listen(port, () => {
    console.log(`ICOM Writer app listening on port: ${port} (HTTP fallback)`);
  });
} else {
  server.listen(port, () => {
    console.log(`ICOM Writer app listening on port: ${port} (HTTPS)`);
  });
}































































