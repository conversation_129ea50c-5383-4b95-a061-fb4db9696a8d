#!/usr/bin/env python3
import os
import socket
import argparse
from typing import Dict
import qrcode
from qrcode.constants import ERROR_CORRECT_L, ERROR_CORRECT_M, ERROR_CORRECT_Q, ERROR_CORRECT_H

# ---------- QR helpers ----------

EC_MAP = {"L": ERROR_CORRECT_L, "M": ERROR_CORRECT_M, "Q": ERROR_CORRECT_Q, "H": ERROR_CORRECT_H}

def esc(s: str) -> str:
    """Escape special characters for WIFI payload."""
    if s is None:
        return ""
    return (
        s.replace("\\", "\\\\")
         .replace(";", r"\;")
         .replace(",", r"\,")
         .replace(":", r"\:")
         .replace('"', r'\"')
    )

def wifi_payload(ssid: str, password: str, auth: str = "WPA", hidden: bool = False) -> str:
    A = (auth or "WPA").upper()
    # Normalize auth names
    if A not in ("WPA", "WPA2", "WEP", "NOPASS"):
        A = "WPA"
    if A == "NOPASS":
        password = ""
    parts = ["WIFI:", f"T:{'nopass' if A=='NOPASS' else A};", f"S:{esc(ssid)};"]
    if password:
        parts.append(f"P:{esc(password)};")
    if hidden:
        parts.append("H:true;")
    parts.append(";")  # terminator
    return "".join(parts)

def make_qr(data: str, out_path: str, ec: str = "M", box: int = 10, border: int = 4):
    qr = qrcode.QRCode(
        version=None,
        error_correction=EC_MAP.get(ec.upper(), ERROR_CORRECT_M),
        box_size=box,
        border=border,
    )
    qr.add_data(data)
    qr.make(fit=True)
    img = qr.make_image(fill_color="black", back_color="white")
    os.makedirs(os.path.dirname(out_path) or ".", exist_ok=True)
    img.save(out_path)
    return out_path

def get_local_ip() -> str:
    """Best-effort local LAN IPv4 (not 127.0.0.1)."""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"

# ---------- config parsing ----------

def parse_kv_file(path: str) -> Dict[str, str]:
    """Parse simple key=value lines, ignoring blanks and lines starting with # or ;"""
    kv = {}
    with open(path, "r", encoding="utf-8", errors="replace") as f:
        for raw in f:
            line = raw.strip()
            if not line or line.startswith("#") or line.startswith(";"):
                continue
            if "=" not in line:
                continue
            key, value = line.split("=", 1)
            kv[key.strip()] = value.strip()
    return kv

def to_bool(v: str, default=False) -> bool:
    if v is None:
        return default
    return v.strip().lower() in ("1", "true", "yes", "y", "on")

# ---------- main ----------

def main():
    ap = argparse.ArgumentParser(description="Generate Wi-Fi and local URL QR codes from radio_config.cfg")
    ap.add_argument("--config", "-c", default="radio_config.cfg", help="Path to key=value config file")
    ap.add_argument("--outdir", "-d", default=None, help="Override output directory (else from config or '.')")
    ap.add_argument("--ec", default="M", help="Error correction L/M/Q/H (default M)")
    ap.add_argument("--size", type=int, default=10, help="Box size per module (default 10)")
    ap.add_argument("--border", type=int, default=4, help="Quiet zone modules (default 4)")
    ap.add_argument("--port", type=int, default=3000, help="Port for local URL QR (default 3000)")
    args = ap.parse_args()

    cfg = parse_kv_file(args.config)

    # Read Wi-Fi bits
    ssid = cfg.get("wifi_ssid", "")
    password = cfg.get("wifi_password", "")
    auth = cfg.get("wifi_auth", "WPA")
    hidden = to_bool(cfg.get("wifi_hidden"), default=False)

    # If password empty and no explicit auth, assume nopass
    if not password and "wifi_auth" not in cfg:
        auth = "nopass"

    if not ssid:
        raise SystemExit("? Config missing wifi_ssid")

    # Output options
    out_dir = args.outdir or cfg.get("output_dir", ".")
    wifi_filename = cfg.get("wifi_filename", "wifi.png")
    url_filename = cfg.get("url_filename", "local_site.png")

    # Build Wi-Fi QR
    wifi_data = wifi_payload(ssid, password, auth, hidden)
    wifi_out = os.path.join(out_dir, wifi_filename)
    make_qr(wifi_data, wifi_out, args.ec, args.size, args.border)
    print(f"? Wi-Fi QR saved -> {wifi_out}")

    # Build URL QR
    local_ip = get_local_ip()
    url = f"http://{local_ip}:{args.port}"
    url_out = os.path.join(out_dir, url_filename)
    make_qr(url, url_out, args.ec, args.size, args.border)
    print(f"? Local site QR ({url}) saved -> {url_out}")

if __name__ == "__main__":
    main()
