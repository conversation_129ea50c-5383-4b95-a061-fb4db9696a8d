<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Node Keeper</title>
    <link rel="stylesheet" href="./styles.css" />
  </head>
  <body>
    <div class="card drag">
      <div class="row">
        <div id="dot" class="dot"></div>
        <div class="title">Node Keeper</div>
      </div>

      <div class="row info">
        <div>Status: <span id="status">—</span></div>
      </div>
      <div class="row info">
        <div>Restarts (24h): <span id="restarts">0</span></div>
      </div>
      <div class="row info small">
        <div id="project">Project: —</div>
      </div>

      <div class="row actions">
        <button id="pick">Change Project</button>
        <button id="start">Start</button>
        <button id="stop">Stop</button>
        <button id="logs">Logs</button>
      </div>
    </div>

    <script src="./renderer.js"></script>
  </body>
</html>
