html, body {
  margin: 0;
  background: transparent;
  font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, <PERSON><PERSON>, <PERSON><PERSON>, "Apple Color Emoji", "Segoe UI Emoji";
}

.card {
  width: 320px;
  height: 140px;
  padding: 10px 12px;
  box-sizing: border-box;
  border-radius: 12px;
  background: #0b1220;
  color: #e8f0ff;
  border: 1px solid #1c2740;
  box-shadow: 0 10px 40px rgba(0,0,0,.35);
  -webkit-app-region: drag; /* allows dragging the frameless window */
}

.row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title {
  font-weight: 700;
  font-size: 16px;
  letter-spacing: .2px;
}

.info {
  font-size: 14px;
  margin-top: 4px;
  opacity: 0.95;
}

.small { 
  opacity: 0.75; 
  font-size: 12px;
}

.actions {
  margin-top: 8px;
  gap: 6px;
}

.actions button {
  -webkit-app-region: no-drag; /* buttons must be clickable */
  border: 1px solid #344674;
  background: #152038;
  color: #cfe0ff;
  border-radius: 8px;
  padding: 6px 10px;
  font-size: 13px;
  cursor: pointer;
  min-height: 28px;
}
.actions button:hover {
  background: #1c2a4a;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: gray;
  box-shadow: 0 0 8px rgba(255,255,255,.15);
}

.drag { -webkit-app-region: drag; }

/* Optimizations for 385x845 resolution */
@media (max-width: 400px) {
  .card {
    width: 100%;
    max-width: 385px;
    height: auto;
    min-height: 180px;
    padding: 18px;
    border-radius: 8px;
  }

  .title {
    font-size: 20px;
    letter-spacing: 0.3px;
  }

  .info {
    font-size: 18px;
    margin-top: 8px;
  }

  .small {
    font-size: 16px;
    opacity: 0.8;
  }

  .actions {
    margin-top: 16px;
    gap: 10px;
    flex-wrap: wrap;
  }

  .actions button {
    padding: 10px 14px;
    font-size: 16px;
    min-height: 42px;
    flex: 1;
    min-width: 80px;
    border-radius: 6px;
    font-weight: 500;
  }

  .dot {
    width: 16px;
    height: 16px;
  }

  .row {
    gap: 10px;
  }
}
