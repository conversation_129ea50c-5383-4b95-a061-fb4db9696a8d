{"version": 3, "file": "errorMessages.js", "sourceRoot": "", "sources": ["../src/errorMessages.ts"], "names": [], "mappings": ";;;AAAa,QAAA,mBAAmB,GAAG;;;;;;CAMlC,CAAA", "sourcesContent": ["export const authorEmailIsMissed = `Please specify author 'email' in the application package.json\n\nSee https://docs.npmjs.com/files/package.json#people-fields-author-contributors\n\nIt is required to set Linux .deb package maintainer. Or you can set maintainer in the custom linux options.\n(see https://www.electron.build/linux).\n`\n"]}