﻿# CS-F7500 UIA automation - v3.21
# - Reads file names/locations and options from radio_config.cfg
# - Strict COM-port nav (Alt+F, Right x2 -> Com Port, Down -> Select COM Port, Enter)
# - NEW: write_to_radio() — Program menu (Alt+F, Right x3 -> Program, Down -> Write TR, Enter)
# - Optional auto_write_after_group per cfg [nav] auto_write_after_group=true/false (default True)
# - Kills EX3852A.EXE before start
# - Absolute-from-Home with HARD HOME, F2 for Name, Mode via dropdown (Enter, Down, Enter)
# - Detached launch + PID/title attach
# - ASCII-only

import os
import re
import time
import csv
import json
import subprocess
from collections import defaultdict
from datetime import datetime
from pywinauto import Desktop
from pywinauto.application import Application
from pywinauto import keyboard
import configparser

# =========================
# DEFAULTS (overridable by CFG)
# =========================
WORKDIR_DEFAULT = r"C:\cc\icom"
EXE_PATH_DEFAULT = r"C:\Program Files (x86)\Icom\CS-F7500\EX3852A.EXE"
NEW_CHANNEL_DEFAULT = "new_channel.csv"
RADIO_CONFIG_CSV_DEFAULT = "radio_config.csv"
CFG_DEFAULT = "radio_config.cfg"

WINDOW_TITLE_KEYS = ["CS-F7500", "Icom", "EX3852A"]

# waits (seconds)
POST_LAUNCH_WAIT   = 1.5
ATTACH_TIMEOUT     = 60.0
POST_ATTACH_SETTLE = 1.0
POST_MAXIMIZE_WAIT = 1.0
TREE_RETRY_SECONDS = 25
COM_DIALOG_WAIT    = 15.0
WRITE_POPUP_WAIT   = 15.0
WRITE_FINISH_WAIT  = 180.0

# will be set by cfg or defaults
WORKDIR = WORKDIR_DEFAULT
EXE_PATH = EXE_PATH_DEFAULT
CSV_FILE = os.path.join(WORKDIR_DEFAULT, NEW_CHANNEL_DEFAULT)
RADIO_CONFIG_FILE = os.path.join(WORKDIR_DEFAULT, RADIO_CONFIG_CSV_DEFAULT)
CFG_PATH = os.path.join(WORKDIR_DEFAULT, CFG_DEFAULT)

GRID_NUDGE_POINT = (900, 400)  # can override via cfg [grid] nudge=900,400

# Absolute hops (RIGHT from Home) — override from cfg [grid] home_to_col=...
HOME_TO_COL = {
    "name":  4,
    "mode":  5,
    "rx":    6,
    "tx":    7,
    "rxctc": 9,
    "txctc": 10,
}

MODE_DROPDOWN_DOWN_PRESSES = 1     # cfg [nav] mode_down=
DEV_OPTION_STRICT_NAV = True       # cfg [nav] strict_nav=true/false
AUTO_WRITE_AFTER_GROUP = True      # cfg [nav] auto_write_after_group=true/false

ALWAYS_SET_TONE_MODE = False
PAUSE_ON_ERROR = True
LOG_TO_FILE = True

LOG_FILE = os.path.join(WORKDIR_DEFAULT, "programming_log.txt")

# =========================
# LOGGING
# =========================
def log(msg: str):
    ts = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
    line = f"{ts} {msg}"
    print(line)
    if LOG_TO_FILE:
        try:
            with open(LOG_FILE, "a", encoding="utf-8") as f:
                f.write(line + "\n")
        except Exception:
            pass

def pause(label=""):
    if PAUSE_ON_ERROR:
        try:
            input(f"[{label}] Press Enter to continue...")
        except Exception:
            time.sleep(2)

# =========================
# CFG LOADING
# =========================
def _parse_home_to_col(val: str):
    # accepts JSON, or "name=4,mode=5,rx=6,tx=7,rxctc=9,txctc=10"
    if not val:
        return {}
    val = val.strip()
    try:
        obj = json.loads(val)
        if isinstance(obj, dict):
            return {str(k).lower(): int(obj[k]) for k in obj}
    except Exception:
        pass
    out = {}
    for part in re.split(r"[;,]\s*", val):
        if not part:
            continue
        if "=" in part:
            k, v = part.split("=", 1)
            try:
                out[k.strip().lower()] = int(v.strip())
            except Exception:
                pass
    return out

def _parse_point(val: str):
    try:
        x_str, y_str = [s.strip() for s in val.split(",")]
        return (int(x_str), int(y_str))
    except Exception:
        return None

def _normalize_com(s):
    s = (s or "").strip().upper()
    if not s:
        return ""
    if s.startswith("COM"):
        return s
    if s.isdigit():
        return "COM" + s
    return s.replace(" ", "").replace("-", "")

def load_cfg_and_apply():
    global WORKDIR, EXE_PATH, CSV_FILE, RADIO_CONFIG_FILE, CFG_PATH
    global GRID_NUDGE_POINT, HOME_TO_COL, MODE_DROPDOWN_DOWN_PRESSES
    global DEV_OPTION_STRICT_NAV, LOG_FILE, AUTO_WRITE_AFTER_GROUP

    env_cfg = os.environ.get("ICOM_CFG")
    if env_cfg and os.path.isfile(env_cfg):
        CFG_PATH = env_cfg
    elif os.path.isfile(CFG_PATH):
        pass
    else:
        if os.path.isfile(CFG_DEFAULT):
            CFG_PATH = os.path.abspath(CFG_DEFAULT)

    cfg = configparser.ConfigParser()
    read_ok = cfg.read(CFG_PATH, encoding="utf-8") if os.path.isfile(CFG_PATH) else []
    if read_ok:
        log(f"Loaded CFG: {CFG_PATH}")
    else:
        log("CFG not found; using defaults.")

    # [paths]
    WORKDIR = cfg.get("paths", "workdir", fallback=WORKDIR_DEFAULT)
    EXE_PATH = cfg.get("paths", "exe_path", fallback=EXE_PATH_DEFAULT)
    new_ch = cfg.get("paths", "new_channel", fallback=NEW_CHANNEL_DEFAULT)
    rad_csv = cfg.get("paths", "radio_config", fallback=RADIO_CONFIG_CSV_DEFAULT)

    if not os.path.isabs(new_ch):
        CSV_FILE = os.path.join(WORKDIR, new_ch)
    else:
        CSV_FILE = new_ch

    if not os.path.isabs(rad_csv):
        RADIO_CONFIG_FILE = os.path.join(WORKDIR, rad_csv)
    else:
        RADIO_CONFIG_FILE = rad_csv

    LOG_FILE = os.path.join(WORKDIR, "programming_log.txt")

    # [nav]
    MODE_DROPDOWN_DOWN_PRESSES = cfg.getint("nav", "mode_down", fallback=MODE_DROPDOWN_DOWN_PRESSES)
    DEV_OPTION_STRICT_NAV = cfg.getboolean("nav", "strict_nav", fallback=DEV_OPTION_STRICT_NAV)
    AUTO_WRITE_AFTER_GROUP = cfg.getboolean("nav", "auto_write_after_group", fallback=AUTO_WRITE_AFTER_GROUP)

    # [grid]
    nudge_raw = cfg.get("grid", "nudge", fallback="")
    if nudge_raw:
        pt = _parse_point(nudge_raw)
        if pt:
            GRID_NUDGE_POINT = pt

    h2c_raw = cfg.get("grid", "home_to_col", fallback="")
    if h2c_raw:
        overrides = _parse_home_to_col(h2c_raw)
        if overrides:
            HOME_TO_COL.update(overrides)

    try:
        os.makedirs(WORKDIR, exist_ok=True)
    except Exception:
        pass

    log(f"WORKDIR={WORKDIR}")
    log(f"EXE_PATH={EXE_PATH}")
    log(f"CSV_FILE={CSV_FILE}")
    log(f"RADIO_CONFIG_FILE={RADIO_CONFIG_FILE}")
    log(f"STRICT_NAV={DEV_OPTION_STRICT_NAV} MODE_DOWN={MODE_DROPDOWN_DOWN_PRESSES} AUTO_WRITE_AFTER_GROUP={AUTO_WRITE_AFTER_GROUP}")
    log(f"GRID_NUDGE_POINT={GRID_NUDGE_POINT}")
    log(f"HOME_TO_COL={HOME_TO_COL}")

    inline_map = {}
    if "radios" in cfg:
        for k, v in cfg.items("radios"):
            key = (k or "").strip().lower()
            val = (v or "").strip()
            if key:
                inline_map[key] = _normalize_com(val)
    return inline_map

# =========================
# CSV HELPERS
# =========================
def load_csv_with_fallbacks(path):
    encodings = ["utf-8-sig", "utf-8", "cp1252", "latin-1"]
    last_err = None
    for enc in encodings:
        try:
            with open(path, newline="", encoding=enc) as f:
                return list(csv.DictReader(f)), enc
        except Exception as e:
            last_err = e
    raise last_err

def read_radio_config_csv(path):
    try:
        dict_rows, used = load_csv_with_fallbacks(path)
        log(f"Loaded radio_config CSV with encoding: {used}")
    except Exception as e:
        log(f"WARN reading radio_config CSV: {e}")
        return {}
    out = {}
    for r in dict_rows:
        rdradio = (r.get("radio") or r.get("unit") or r.get("device") or r.get("radio_id") or "").strip().lower()
        com = _normalize_com(r.get("comport") or r.get("com_port") or r.get("com") or r.get("port") or "")
        if rdradio:
            out[rdradio] = com
    return out

def parse_new_channels(dict_rows):
    out = []
    def get(r, *names):
        for n in names:
            v = r.get(n)
            if v is not None:
                return v.strip()
        return ""
    for r in dict_rows:
        radio  = get(r, "radio", "unit", "device", "radio_id")
        name   = get(r, "name")
        rx     = get(r, "rx")
        tx     = get(r, "tx")
        rxtone = get(r, "rxtone", "rx_ctone")
        txtone = get(r, "txtone", "tx_ctone")
        out.append({"radio": radio, "name": name, "rx": rx, "tx": tx, "rxtone": rxtone, "txtone": txtone})
    return out

# =========================
# PRELAUNCH CLEANUP
# =========================
def kill_existing_processes():
    names = ["EX3852A.EXE"]
    for name in names:
        try:
            r = subprocess.run(["taskkill", "/F", "/IM", name], capture_output=True, text=True, shell=False)
            out = (r.stdout or "") + (r.stderr or "")
            if r.returncode == 0:
                log(f"Killed existing {name}")
            else:
                low = out.lower()
                if "no instance" in low or "not found" in low:
                    log(f"No running {name} to kill.")
                else:
                    log(f"WARN taskkill {name}: rc={r.returncode} msg='{out.strip()[:200]}'")
        except Exception as e:
            log(f"WARN taskkill {name}: {e}")
    time.sleep(0.8)

# =========================
# WINDOW / ATTACH
# =========================
def list_top_windows():
    try:
        wins = Desktop(backend="uia").windows()
        for w in wins:
            try:
                log(f"WIN pid={w.process_id} vis={w.is_visible()} title='{w.window_text()}' class='{w.friendly_class_name()}'")
            except Exception:
                pass
    except Exception:
        pass

def try_connect_by_pid(pid):
    try:
        app = Application(backend="uia").connect(process=pid, timeout=5)
        win = app.top_window()
        return app, win
    except Exception:
        return None, None

def find_window_by_title_keys():
    wins = Desktop(backend="uia").windows()
    for w in wins:
        try:
            title = (w.window_text() or "").strip()
            if not title:
                continue
            for key in WINDOW_TITLE_KEYS:
                if key.lower() in title.lower() and w.is_visible():
                    return w
        except Exception:
            continue
    return None

def has_tree_control(win):
    try:
        t = win.child_window(control_type="Tree")
        return bool(t and t.exists())
    except Exception:
        return False

def attach_or_launch():
    log("Launching detached...")
    try:
        p = subprocess.Popen([EXE_PATH], shell=False)
        launch_pid = p.pid
        log(f"Launched PID={launch_pid}")
    except Exception as e:
        raise RuntimeError(f"launch failed: {e}")
    time.sleep(POST_LAUNCH_WAIT)

    t0 = time.time()
    app = None
    main = None
    last_dump = 0
    while time.time() - t0 < ATTACH_TIMEOUT:
        if app is None:
            app, win = try_connect_by_pid(launch_pid)
            if app and win:
                main = win
                log("Attached by PID to top_window()")

        if main is None:
            w = find_window_by_title_keys()
            if w:
                try:
                    app = Application(backend="uia").connect(handle=w.handle)
                    main = app.window(handle=w.handle)
                    log(f"Attached by title to '{main.window_text()}' (pid={w.process_id})")
                except Exception:
                    main = None

        if main:
            try:
                main.wait("visible", timeout=5)
                break
            except Exception:
                main = None

        if time.time() - last_dump > 5:
            log("Waiting for main window... listing top windows:")
            list_top_windows()
            last_dump = time.time()
        time.sleep(0.4)

    if not main:
        raise RuntimeError("attach_or_launch: could not find a running main window")

    try:
        main.maximize(); time.sleep(POST_MAXIMIZE_WAIT)
    except Exception as e:
        log(f"WARN maximize: {e}")
    time.sleep(POST_ATTACH_SETTLE)
    return app, main

# =========================
# TREE SELECT
# =========================
def _expand(item):
    try:
        item.expand()
    except Exception:
        try:
            item.double_click_input()
        except Exception:
            pass
    time.sleep(0.15)

def _find_tree_root(main):
    try:
        t = main.child_window(control_type="Tree")
        if t.exists():
            return t
    except Exception:
        pass
    for el in main.descendants():
        try:
            if getattr(el.element_info, "class_name", "") == "TreeView":
                return el
        except Exception:
            continue
    return None

def _find_treeitem(root, label):
    want = label.strip().lower()
    for it in root.descendants(control_type="TreeItem"):
        nm = (it.window_text() or "").strip().lower()
        if nm == want:
            return it
    return None

def select_tree_items(main):
    deadline = time.time() + TREE_RETRY_SECONDS
    MEM, Z1A, Z1B = "Memory CH", "1: Zone 1", "Zone 1"
    while time.time() < deadline:
        root = _find_tree_root(main)
        if not root:
            keyboard.send_keys("{F6}"); time.sleep(0.5); continue

        mem = _find_treeitem(root, MEM)
        if not mem:
            for ti in root.descendants(control_type="TreeItem"):
                _expand(ti)
            mem = _find_treeitem(root, MEM)
        if not mem:
            time.sleep(0.5); continue

        try: mem.click_input()
        except Exception: pass
        _expand(mem)

        zone = _find_treeitem(root, Z1A) or _find_treeitem(root, Z1B)
        if zone:
            try: zone.click_input()
            except Exception:
                try: zone.double_click_input()
                except Exception: pass
            log("Selected Memory CH -> Zone 1")
            return
        time.sleep(0.3)
    raise RuntimeError("Could not select 'Memory CH' -> 'Zone 1'")

# =========================
# GRID NAV HELPERS (ABSOLUTE + HARD HOME)
# =========================
def nudge_grid(main):
    try:
        main.click_input(coords=GRID_NUDGE_POINT); log("Nudged grid focus.")
    except Exception:
        pass
    time.sleep(0.25)

def ensure_nav_mode():
    keyboard.send_keys("{ESC}"); time.sleep(0.06)

def hard_home_to_col0():
    ensure_nav_mode()
    keyboard.send_keys("{HOME}"); time.sleep(0.08)
    keyboard.send_keys("{HOME}"); time.sleep(0.08)
    for _ in range(8):
        keyboard.send_keys("^({LEFT})"); time.sleep(0.02)

def hop_from_home(rights: int):
    hard_home_to_col0()
    n = max(0, int(rights or 0))
    if n:
        keyboard.send_keys("{RIGHT %d}" % n)
    time.sleep(0.08)

def goto_col(key: str):
    offs = HOME_TO_COL.get(key)
    if offs is None:
        raise KeyError("Unknown column key: %s" % key)
    hop_from_home(offs)

def enter_value_here(text: str):
    keyboard.send_keys("{ENTER}"); time.sleep(0.12)
    if text:
        keyboard.send_keys(str(text), with_spaces=True)
    keyboard.send_keys("{ENTER}"); time.sleep(0.12)
    ensure_nav_mode()

def enter_text_cell(col_key: str, text: str, use_f2: bool = False):
    goto_col(col_key)
    if use_f2:
        keyboard.send_keys("{F2}"); time.sleep(0.12)
    else:
        keyboard.send_keys("{ENTER}"); time.sleep(0.12)
    if text:
        keyboard.send_keys(str(text), with_spaces=True)
    keyboard.send_keys("{ENTER}"); time.sleep(0.12)
    ensure_nav_mode()

def pick_mode_dropdown():
    goto_col("mode")
    keyboard.send_keys("{ENTER}"); time.sleep(0.12)
    d = max(0, MODE_DROPDOWN_DOWN_PRESSES)
    if d:
        keyboard.send_keys("{DOWN %d}" % d); time.sleep(0.08)
    keyboard.send_keys("{ENTER}"); time.sleep(0.12)
    ensure_nav_mode()

# =========================
# STRICT COM-PORT NAVIGATION
# =========================
def open_com_port_select_popup_strict():
    keyboard.send_keys("%f"); time.sleep(0.15)        # Alt+F
    keyboard.send_keys("{RIGHT 2}"); time.sleep(0.15)  # to "Com Port"
    keyboard.send_keys("{DOWN}"); time.sleep(0.10)     # "Select COM Port"
    keyboard.send_keys("{ENTER}"); time.sleep(0.25)    # open popup
    t0 = time.time()
    while time.time() - t0 < COM_DIALOG_WAIT:
        dlg = find_com_select_popup()
        if dlg is not None:
            log("COM Select popup detected (strict path).")
            return dlg
        time.sleep(0.2)
    log("WARN: strict COM Select popup not detected in time.")
    return None

def find_com_select_popup():
    wins = Desktop(backend="uia").windows()
    for w in wins:
        try:
            if not w.is_visible(): 
                continue
            for el in w.descendants():
                try:
                    nm = (el.window_text() or "").strip().upper()
                except Exception:
                    continue
                if not nm:
                    continue
                if nm.startswith("COM") or " COM" in nm:
                    return w
        except Exception:
            continue
    return None

def select_com_in_popup(com_target):
    dlg = find_com_select_popup()
    if dlg is None:
        log("ERROR: COM popup not found for selection.")
        return False
    try: dlg.set_focus()
    except Exception: pass
    time.sleep(0.15)

    target = (com_target or "").strip().upper()
    found = False
    try:
        for el in dlg.descendants():
            try:
                clas = el.friendly_class_name()
                nm = (el.window_text() or "").strip().upper()
            except Exception:
                continue
            if clas in ("ListItem", "ListBoxItem", "TreeItem") and nm == target:
                try:
                    el.click_input(); time.sleep(0.1)
                    el.double_click_input()
                    found = True
                    break
                except Exception:
                    pass
    except Exception:
        pass

    if not found and target:
        try:
            keyboard.send_keys(target + "{ENTER}")
            found = True
        except Exception:
            pass

    closed = False
    try:
        for b in dlg.descendants(control_type="Button"):
            nm = (b.window_text() or "").strip().lower()
            if nm in ("ok", "select", "apply", "close", "set"):
                try:
                    b.click_input(); closed = True; break
                except Exception:
                    pass
    except Exception:
        pass
    if not closed:
        try:
            keyboard.send_keys("{ENTER}"); time.sleep(0.2)
            closed = (find_com_select_popup() is None)
        except Exception:
            pass

    if found:
        log(f"Selected COM '{com_target}' in popup.")
    else:
        log(f"WARN: could not confirm selection of '{com_target}' (may still be applied).")
    if closed:
        log("COM popup closed.")
    else:
        log("WARN: COM popup might still be open.")
    return found

# =========================
# PROGRAM MENU: WRITE TO RADIO (STRICT)
# =========================
def open_program_menu_strict():
    """
    Menubar order: File, View, COM Port, Program
    Program is the 4th item -> starting at File: Alt+F, Right x3
    """
    keyboard.send_keys("%f")          # Alt+F
    time.sleep(0.15)
    keyboard.send_keys("{RIGHT 3}")   # to Program
    time.sleep(0.15)

def find_write_popup_or_progress():
    """
    Try to find a confirmation/progress dialog for writing.
    We match on visible windows containing 'write', 'program',
