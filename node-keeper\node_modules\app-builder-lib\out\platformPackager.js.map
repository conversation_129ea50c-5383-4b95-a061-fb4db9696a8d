{"version": 3, "file": "platformPackager.js", "sourceRoot": "", "sources": ["../src/platformPackager.ts"], "names": [], "mappings": ";;;AAy1BA,4CAEC;AAED,0EAeC;AAGD,oCAEC;AAED,sCAEC;AAr3BD,2CAAiG;AACjG,+CAeqB;AAErB,0CAAqC;AACrC,uCAA+B;AAE/B,6BAA4B;AAC5B,uCAAmC;AACnC,4DAA2D;AAC3D,8CAA8C;AAC9C,gDAA6D;AAE7D,+CAA8I;AAC9I,uDAA4E;AAC5E,2CAAwD;AACxD,mCAcgB;AAChB,kDAA2D;AAC3D,wDAA+I;AAC/I,wDAAmE;AAgBnE,MAAsB,gBAAgB;IACpC,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAA;IAC1B,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAA;IACpC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAA;IAC7B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAA;IACzB,CAAC;IAID,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAA;IACjC,CAAC;IAMD,YACW,IAAc,EACd,QAAkB;QADlB,SAAI,GAAJ,IAAI,CAAU;QACd,aAAQ,GAAR,QAAQ,CAAU;QANZ,kBAAa,GAAG,IAAI,eAAI,CAAgB,GAAG,EAAE,CAAC,IAAA,+BAAgB,EAAC,IAAA,kBAAO,EAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;QAQxH,IAAI,CAAC,4BAA4B,GAAG,gBAAgB,CAAC,qCAAqC,CAAE,IAAI,CAAC,MAAc,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAA;QAChJ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IAClD,CAAC;IAED,IAAI,WAAW;QACb,MAAM,WAAW,GAAG,IAAI,CAAC,4BAA4B,CAAC,WAAW,CAAA;QACjE,0FAA0F;QAC1F,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;YACzB,OAAO,QAAQ,CAAA;QACjB,CAAC;QACD,OAAO,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,QAAQ,CAAA;IAC3D,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAA;IAC9B,CAAC;IAID,2BAA2B;IACjB,cAAc,CAAC,OAAgB;QACvC,OAAO,IAAI,iBAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAAA;IACxE,CAAC;IAEO,MAAM,CAAC,qCAAqC,CAAC,OAAsB;QACzE,OAAO,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;IACxD,CAAC;IAID,cAAc;QACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACxC,IAAI,IAAA,8BAAe,EAAC,QAAQ,CAAC,EAAE,CAAC;YAC9B,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,iCAAiC,EAAE,EAAE,8CAA8C,CAAC,CAAA;YACvG,OAAO,EAAE,CAAA;QACX,CAAC;aAAM,CAAC;YACN,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAA;QACxB,CAAC;IACH,CAAC;IAED,UAAU,CAAC,YAA4B;QACrC,mCAAmC;QACnC,MAAM,QAAQ,GAAG,aAAa,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAC7G,OAAO,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAA;IACpH,CAAC;IAED,gBAAgB;QACd,mCAAmC;QACnC,OAAO,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,4BAA4B,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;IACtJ,CAAC;IAES,gBAAgB,CAAC,MAAc,EAAE,IAAU;QACnD,OAAO,CACL,IAAI,CAAC,eAAe,CAAC,WAAW;YAChC,IAAI,CAAC,IAAI,CACP,MAAM,EACN,GAAG,IAAI,CAAC,QAAQ,CAAC,qBAAqB,GAAG,IAAA,4BAAa,EAAC,IAAI,EAAE,IAAI,CAAC,4BAA4B,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAClK,CACF,CAAA;IACH,CAAC;IAED,uBAAuB,CAAC,IAAY,EAAE,MAAqB,EAAE,IAAiB,EAAE,gBAAgC;QAC9G,OAAO,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC;YAC1C,IAAI;YACJ,gBAAgB;YAChB,MAAM;YACN,IAAI;YACJ,QAAQ,EAAE,IAAI;SACf,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,MAAc,EAAE,IAAU,EAAE,OAAsB,EAAE,WAA6B;QAC1F,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACrD,MAAM,IAAI,CAAC,MAAM,CAAC;YAChB,MAAM;YACN,SAAS;YACT,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAgC;YAC5D,IAAI;YACJ,4BAA4B,EAAE,IAAI,CAAC,4BAA4B;YAC/D,OAAO;SACR,CAAC,CAAA;QACF,IAAI,CAAC,4BAA4B,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,CAAA;IAC1E,CAAC;IAES,4BAA4B,CAAC,SAAiB,EAAE,IAAU,EAAE,OAAsB,EAAE,WAA6B;QACzH,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,IAAI,IAAI,EAAE,CAAC;YACrD,gBAAgB,CAAC,iBAAiB,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;YACzE,OAAM;QACR,CAAC;QAED,WAAW,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;YACzB,6IAA6I;YAC7I,MAAM,cAAc,GAAG,IAAI,+BAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;YACxE,gBAAgB,CAAC,iBAAiB,CAAC,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;YAC5E,MAAM,cAAc,CAAC,UAAU,EAAE,CAAA;YAEjC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;oBACvE,MAAM,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;gBACrC,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,OAAsB,EAAE,WAA6B,EAAE,SAAiB,EAAE,IAAU;QACnH,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAC5B,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAA;YACpD,CAAC;QACH,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,WAAoB,EAAE,SAAiB,EAAE,OAA+B;QACnG,MAAM,IAAI,GAAG,WAAW;YACtB,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;YACjC,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG;gBAC9B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,MAAM,EAAE,UAAU,CAAC;gBACzE,CAAC,CAAC,SAAS,CAAA;QACf,OAAO,IAAA,6BAAe,EAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IACnG,CAAC;IAED,4BAA4B,CAAC,MAAc,EAAE,IAAU,EAAE,kBAAgD;QACvG,OAAO;YACL,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;YACjG,kBAAkB;YAClB,YAAY,EAAE,MAAM;YACpB,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAA;IACH,CAAC;IAES,KAAK,CAAC,MAAM,CAAC,WAA8B;;QACnD,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YAC7C,OAAM;QACR,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YAC1C,OAAM;QACR,CAAC;QAED,+KAA+K;QAC/K,OAAO,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAA;QAEnC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,IAAI,EAAE,4BAA4B,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,WAAW,CAAA;QAE7G,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;YAC7B,SAAS;YACT,MAAM;YACN,IAAI;YACJ,OAAO;YACP,QAAQ,EAAE,IAAI;YACd,oBAAoB,EAAE,YAAY;SACnC,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QAE3D,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YAC1C,OAAM;QACR,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;QACrC,kBAAG,CAAC,IAAI,CACN;YACE,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,mBAAI,CAAC,IAAI,CAAC;YAChB,CAAC,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,EAAE,SAAS,CAAC,OAAO;YACxC,SAAS,EAAE,kBAAG,CAAC,QAAQ,CAAC,SAAS,CAAC;SACnC,EACD,WAAW,CACZ,CAAA;QAED,MAAM,SAAS,CAAC,gCAAgC,CAAC;YAC/C,QAAQ,EAAE,IAAI;YACd,SAAS;YACT,YAAY;YACZ,IAAI,EAAE,mBAAI,CAAC,IAAI,CAAC;YAChB,OAAO,EAAE,SAAS,CAAC,OAAO;SAC3B,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC;YAC/B,SAAS;YACT,MAAM;YACN,IAAI;YACJ,OAAO;YACP,QAAQ,EAAE,IAAI;YACd,oBAAoB,EAAE,YAAY;SACnC,CAAC,CAAA;QAEF,MAAM,eAAe,GAAqB,EAAE,CAAA;QAE5C,MAAM,qBAAqB,GAAG,CAAC,QAAmC,EAAE,EAAE;YACpE,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;gBACrB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;oBAC/B,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;gBACtE,CAAC;YACH,CAAC;QACH,CAAC,CAAA;QAED,MAAM,sBAAsB,GAAG,IAAI,CAAC,4BAA4B,CAAC,MAAM,EAAE,IAAI,EAAE,4BAA4B,CAAC,CAAA;QAC5G,MAAM,aAAa,GAAG,sBAAsB,CAAC,aAAa,CAAA;QAC1D,MAAM,qBAAqB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,SAAS,EAAE,sBAAsB,CAAC,CAAA;QAChG,qBAAqB,CAAC,qBAAqB,CAAC,CAAA;QAC5C,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,SAAS,EAAE,sBAAsB,CAAC,CAAA;QAC7F,qBAAqB,CAAC,iBAAiB,CAAC,CAAA;QAExC,MAAM,WAAW,GAAqB;YACpC,SAAS;YACT,MAAM;YACN,IAAI;YACJ,OAAO;YACP,QAAQ,EAAE,IAAI;YACd,oBAAoB,EAAE,YAAY;SACnC,CAAA;QAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,4BAA4B,CAAC,CAAA;QAC/E,MAAM,aAAa,GACjB,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG;YAC5B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,gBAAgB,EAAE,UAAU,EAAE,WAAW,CAAC;YAC3E,CAAC,CAAC,IAAA,2BAAe,EAAC,SAAS,CAAC;gBAC1B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC;gBACnC,CAAC,CAAC,SAAS,CAAA;QACjB,MAAM,WAAW,GAAG,IAAI,+BAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QACrE,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE,WAAW,EAAE,4BAA4B,EAAE,eAAe,EAAE,aAAa,CAAC,CAAA;QACtK,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;QAE9B,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YAC1C,OAAM;QACR,CAAC;QAED,IAAI,SAAS,CAAC,oBAAoB,IAAI,IAAI,EAAE,CAAC;YAC3C,MAAM,qBAAqB,GAAG,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAA,2BAAe,EAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAA;YAE1H,IAAI,aAAa,GAAyB,IAAI,CAAA;YAC9C,IAAI,CAAC,CAAC,WAAW,IAAI,IAAI,KAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,oBAAoB,CAAA,CAAC,EAAE,CAAC;gBAC5D,aAAa,GAAG,MAAM,IAAA,uBAAW,EAAC,EAAE,aAAa,EAAE,qBAAqB,EAAE,wBAAwB,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,qBAAqB,EAAE,CAAC,CAAA;YAC/J,CAAC;YAED,MAAM,SAAS,CAAC,oBAAoB,CAAC;gBACnC,QAAQ,EAAE,IAAI;gBACd,SAAS;gBACT,aAAa;gBACb,YAAY;aACb,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YAC1C,OAAM;QACR,CAAC;QAED,MAAM,wBAAwB,GAAG,IAAI,CAAC,8BAA8B,CAAC,WAAW,CAAC,CAAA;QACjF,MAAM,IAAA,uBAAS,EAAC,qBAAqB,EAAE,wBAAwB,CAAC,CAAA;QAChE,MAAM,IAAA,uBAAS,EAAC,iBAAiB,EAAE,wBAAwB,CAAC,CAAA;QAE5D,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YAC1C,OAAM;QACR,CAAC;QAED,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAA;QAE1C,IAAI,SAAS,CAAC,SAAS,IAAI,IAAI,EAAE,CAAC;YAChC,MAAM,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;QACxC,CAAC;QAED,MAAM,MAAM,GAAG,WAAW,IAAI,IAAI,CAAA;QAClC,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAA;QAEjG,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,CAAA,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAA;QAC5C,CAAC;QACD,IAAI,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,mCAAI,IAAI,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,IAAI,EAAE,4BAA4B,EAAE,OAAO,CAAC,CAAA;QAC1G,CAAC;IACH,CAAC;IAED,iDAAiD;IACvC,KAAK,CAAC,kBAAkB,CAAC,WAA6B;QAC9D,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC;YACtC,OAAM;QACR,CAAC;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAA;QACrE,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAA;IACtD,CAAC;IAEO,kBAAkB,CAAC,KAAoB;QAC7C,MAAM,MAAM,GAAiB;YAC3B,OAAO,EAAE,mBAAW,CAAC,EAAE;YACvB,yBAAyB,EAAE,KAAK,CAAC,yBAAyB;SAC3D,CAAA;QACD,6MAA6M;QAC7M,IAAI,KAAK,CAAC,SAAS,IAAI,IAAI,EAAE,CAAC;YAC5B,MAAM,CAAC,qBAAa,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,SAAS,CAAA;QACnD,CAAC;QACD,IAAI,KAAK,CAAC,sBAAsB,IAAI,IAAI,EAAE,CAAC;YACzC,MAAM,CAAC,qBAAa,CAAC,sBAAsB,CAAC,GAAG,KAAK,CAAC,sBAAsB,CAAA;QAC7E,CAAC;QACD,IAAI,KAAK,CAAC,oCAAoC,IAAI,IAAI,EAAE,CAAC;YACvD,MAAM,CAAC,qBAAa,CAAC,oCAAoC,CAAC,GAAG,KAAK,CAAC,oCAAoC,CAAA;QACzG,CAAC;QACD,IAAI,KAAK,CAAC,6BAA6B,IAAI,IAAI,EAAE,CAAC;YAChD,MAAM,CAAC,qBAAa,CAAC,6BAA6B,CAAC,GAAG,KAAK,CAAC,6BAA6B,CAAA;QAC3F,CAAC;QACD,IAAI,KAAK,CAAC,qCAAqC,IAAI,IAAI,EAAE,CAAC;YACxD,MAAM,CAAC,qBAAa,CAAC,qCAAqC,CAAC,GAAG,KAAK,CAAC,qCAAqC,CAAA;QAC3G,CAAC;QACD,IAAI,KAAK,CAAC,mBAAmB,IAAI,IAAI,EAAE,CAAC;YACtC,MAAM,CAAC,qBAAa,CAAC,mBAAmB,CAAC,GAAG,KAAK,CAAC,mBAAmB,CAAA;QACvE,CAAC;QACD,IAAI,KAAK,CAAC,oCAAoC,IAAI,IAAI,EAAE,CAAC;YACvD,MAAM,CAAC,qBAAa,CAAC,oCAAoC,CAAC,GAAG,KAAK,CAAC,oCAAoC,CAAA;QACzG,CAAC;QACD,IAAI,KAAK,CAAC,gCAAgC,IAAI,IAAI,EAAE,CAAC;YACnD,MAAM,CAAC,qBAAa,CAAC,gCAAgC,CAAC,GAAG,KAAK,CAAC,gCAAgC,CAAA;QACjG,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;;;;;;;OASG;IACI,gBAAgB,CAAC,OAAyB,EAAE,KAAiB;QAClE,MAAM,EAAE,SAAS,EAAE,oBAAoB,EAAE,GAAG,OAAO,CAAA;QAEnD,MAAM,GAAG,GAAG;YACV,MAAM,EAAE,MAAM;YACd,GAAG,EAAE,MAAM;YACX,KAAK,EAAE,MAAM;YACb,KAAK,EAAE,EAAE;SACV,CAAC,oBAAoB,CAAC,CAAA;QAEvB,MAAM,cAAc,GAAG,IAAI,YAAY,qBAAa,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAA;QACzG,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,cAAc,GAAG,GAAG,EAAE,CAAC,CAAA;QAE1E,kBAAG,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE,kBAAG,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,EAAE,2BAA2B,CAAC,CAAA;QACzF,OAAO,IAAA,iBAAS,EAAC,kBAAkB,EAAE,KAAK,CAAC,CAAA;IAC7C,CAAC;IAES,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,SAAiB,EAAE,YAAkC,EAAE,IAAU,EAAE,4BAAgC,EAAE,OAAsB;QACzK,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,4BAA4B,CAAC,CAAA;QAC/E,MAAM,MAAM,GAAG,WAAW,IAAI,IAAI,CAAA;QAClC,MAAM,WAAW,GAAG;YAClB,SAAS;YACT,MAAM;YACN,IAAI;YACJ,OAAO;YACP,QAAQ,EAAE,IAAI;YACd,oBAAoB,EAAE,YAAY;SACnC,CAAA;QACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QACvD,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAA;QAC5C,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC;YAC9E,kBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,qFAAqF,CAAC,CAAA;QACvG,CAAC;IACH,CAAC;IAED,2BAA2B;IACjB,8BAA8B,CAAC,WAA6B;QACpE,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,YAAY,CAClB,WAA6B,EAC7B,WAA+B,EAC/B,YAAoB,EACpB,kBAA0B,EAC1B,WAA6B,EAC7B,4BAAgC,EAChC,eAAiC,EACjC,aAAqC;QAErC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAA;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAC1B,MAAM,iBAAiB,GAAG,WAAW,IAAI,IAAI,IAAI,IAAA,uCAAqB,EAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEjF,MAAM,YAAY,GAAG,IAAA,iCAAmB,EAAC,MAAM,EAAE,kBAAkB,EAAE,aAAa,EAAE,4BAA4B,EAAE,IAAI,EAAE,WAAW,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAA;QAC9J,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;gBACnC,OAAO,CAAC,eAAe,GAAG,eAAe,CAAA;YAC3C,CAAC;QACH,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;QACrC,MAAM,WAAW,GAAG,IAAA,mCAAiB,EACnC,MAAM,EACN,MAAM,EACN,iBAAiB;YACf,CAAC,CAAC;gBACE,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI;gBACrC,IAAI,EAAE,8CAA8B;gBACpC,GAAG,MAAM,CAAC,aAAa;aACxB;YACH,CAAC,CAAC,MAAM,CAAC,aAAa,EACxB,SAAS,CAAC,iBAAiB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAC3E,CAAA;QAED,MAAM,gBAAgB,GAAG,CAAC,QAA4B,EAAE,EAAE;YACxD,OAAO,IAAA,+BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC,IAAI,CAAC,KAAK,EAAC,MAAM,EAAC,EAAE;gBAC/H,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,+BAA+B,EAAE,CAAC;oBAChF,MAAM,iBAAiB,GAAG,IAAA,sCAAwB,EAAC,MAAM,EAAE,kBAAkB,EAAE,aAAa,EAAE,4BAA4B,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;oBACtI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,IAAA,yCAAyB,EAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAA;gBAClF,CAAC;gBACD,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;YACjD,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;QAED,IAAI,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACjC,WAAW,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;gBACzB,MAAM,QAAQ,GAAG,MAAM,gBAAgB,CAAC,CAAC,IAAI,yBAAW,CAAC,MAAM,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC,CAAC,CAAA;gBAC/F,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,IAAA,4BAAY,EAAC,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,CAAA;gBACrF,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;YAChC,CAAC,CAAC,CAAA;QACJ,CAAC;aAAM,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YAC/B,+FAA+F;YAC/F,+DAA+D;YAC/D,0CAA0C;YAC1C,MAAM,wBAAwB,GAAG,IAAI,CAAC,8BAA8B,CAAC,WAAW,CAAC,CAAA;YACjF,MAAM,mBAAmB,GAAoB,IAAI,CAAC,EAAE;gBAClD,IAAI,wBAAwB,IAAI,IAAI,EAAE,CAAC;oBACrC,MAAM,MAAM,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAA;oBAC7C,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;wBACnB,OAAO,MAAM,CAAA;oBACf,CAAC;gBACH,CAAC;gBACD,OAAO,WAAW,CAAC,IAAI,CAAC,CAAA;YAC1B,CAAC,CAAA;YACD,WAAW,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;gBACzB,MAAM,QAAQ,GAAG,MAAM,gBAAgB,CAAC,YAAY,CAAC,CAAA;gBACrD,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,IAAA,4BAAY,EAAC,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAA;gBAC7F,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;YAChC,CAAC,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,aAAa,GAAG,IAAA,6BAAe,EAAC,MAAM,EAAE,YAAY,EAAE,kBAAkB,EAAE;gBAC9E,aAAa;gBACb,kBAAkB,EAAE,4BAA4B;gBAChD,YAAY,EAAE,WAAW,CAAC,MAAM;gBAChC,UAAU,EAAE,MAAM;aACnB,CAAC,CAAA;YACF,MAAM,WAAW,GAAG,aAAa,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;YACnE,WAAW,CAAC,OAAO,CACjB,gBAAgB,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,EAAC,QAAQ,EAAC,EAAE;gBACnD,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;oBAC/B,MAAM,IAAA,8BAAc,EAAC,WAAW,EAAE,OAAO,CAAC,CAAA;gBAC5C,CAAC;gBAED,MAAM,IAAI,uBAAY,CAAC,IAAI,EAAE;oBAC3B,kBAAkB;oBAClB,YAAY;oBACZ,OAAO,EAAE,WAAW;oBACpB,aAAa,EAAE,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,YAAY,EAAE;iBAC3C,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACnB,CAAC,CAAC,CACH,CAAA;QACH,CAAC;IACH,CAAC;IAED,6DAA6D;IACnD,OAAO,CAAC,WAA6B,EAAE,MAAe;QAC9D,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAC/B,CAAC;IAED,WAAW;QACT,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;IAC9B,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,kBAAsB;QACrD,IAAI,CAAC,IAAA,2BAAe,EAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAA;QACb,CAAC;QAED,SAAS,YAAY,CAAC,IAAY;YAChC,OAAO,GAAG,IAAI,wEAAwE,CAAA;QACxF,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,MAAa,CAAA;QACxC,IAAI,aAAa,CAAC,aAAa,CAAC,IAAI,IAAI,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAA;QAC9C,CAAC;QACD,IAAI,aAAa,CAAC,iBAAiB,CAAC,IAAI,IAAI,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC,CAAA;QAClD,CAAC;QAED,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,IAAI,CAAA;QAChD,MAAM,MAAM,GAAG,gBAAgB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAA;QAC7E,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;YACrB,MAAM,WAAW,GAAG,MAAM,IAAA,yBAAU,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAA;YAC7E,8BAA8B;YAC9B,IAAI,WAAW,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;gBACjD,kBAAG,CAAC,IAAI,CACN;oBACE,QAAQ,EAAE,kFAAkF;iBAC7F,EACD,2DAA2D,CAC5D,CAAA;YACH,CAAC;YACD,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YACtC,OAAO,EAAE,CAAA;QACX,CAAC;QAED,KAAK,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAE,CAAC;YAC3C,IAAK,MAAc,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAA;YAC/C,CAAC;QACH,CAAC;QACD,OAAO,IAAA,yBAAU,EAAC,EAAE,EAAE,MAAM,CAAC,CAAA;IAC/B,CAAC;IAEM,iBAAiB,CAAC,IAAY;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;IAC5C,CAAC;IAEM,yBAAyB,CAAC,SAAiB;QAChD,OAAO,SAAS,CAAA;IAClB,CAAC;IAED,eAAe,CAAC,SAAiB;QAC/B,IAAI,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAA;QAC7C,CAAC;aAAM,IAAI,IAAA,2BAAe,EAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAC1C,CAAC;aAAM,CAAC;YACN,OAAO,SAAS,CAAA;QAClB,CAAC;IACH,CAAC;IAEM,oBAAoB,CAAC,SAAiB;QAC3C,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,MAAM,EAAE,UAAU,EAAE,WAAW,CAAC,CAAA;IAC7F,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,YAAoB,EAAE,IAAY,EAAE,aAAqB,EAAE,MAAe,EAAE,sBAA+B;QAC1I,IAAI,MAAM,IAAI,sBAAsB,EAAE,CAAC;YACrC,OAAM;QACR,CAAC;QACD,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAA;QAC1F,IAAI,MAAM,EAAE,CAAC;YACX,IAAA,oCAAkB,EAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,EAAE,YAAY,EAAE,aAAa,CAAC,CAAA;YACpF,OAAM;QACR,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QACnC,oHAAoH;QACpH,8FAA8F;QAC9F,IAAI,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACrC,4GAA4G;YAC5G,mGAAmG;YACnG,yCAAyC;YACzC,MAAM,SAAS,GAAkB,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAC/D,IAAI,iBAAiB,GAAG,CAAC,CAAA;YACzB,SAAS,CAAC,IAAI,CAAC,CAAC,QAAgB,EAAE,KAAa,EAAE,EAAE;gBACjD,iBAAiB,GAAG,KAAK,CAAA;gBACzB,OAAO,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;YACnC,CAAC,CAAC,CAAA;YACF,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAA;YACxE,IAAI,QAAQ,GAAG,SAAS,CAAC,MAAM,GAAG,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;YACtH,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,CAAA;YAChD,IAAA,oCAAkB,EAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAA;QACvF,CAAC;aAAM,CAAC;YACN,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,CAAA;YAC7D,MAAM,OAAO,GAAG,MAAM,IAAA,yBAAU,EAAC,QAAQ,CAAC,CAAA;YAC1C,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,GAAG,aAAa,KAAK,QAAQ,qDAAqD,CAAC,CAAA;YACrG,CAAC;iBAAM,CAAC;gBACN,8BAA8B;gBAC9B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;oBACtB,MAAM,IAAI,KAAK,CAAC,GAAG,aAAa,KAAK,QAAQ,oDAAoD,CAAC,CAAA;gBACpG,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,SAAiB,EAAE,MAAe,EAAE,SAAoB,EAAE,sBAA+B;QACxH,MAAM,OAAO,GAAG,MAAM,IAAA,yBAAU,EAAC,SAAS,CAAC,CAAA;QAC3C,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,qBAAqB,SAAS,qDAAqD,CAAC,CAAA;QACtG,CAAC;aAAM,CAAC;YACN,8BAA8B;YAC9B,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,qBAAqB,SAAS,yDAAyD,CAAC,CAAA;YAC1G,CAAC;QACH,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAA;QACpD,MAAM,QAAQ,GAAG,CAAC,SAAS,CAAC,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,CAAA;QACvI,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,QAAQ,EAAE,wBAAwB,EAAE,MAAM,EAAE,sBAAsB,CAAC,CAAA;QAC/G,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,EAAE,sBAAsB,CAAC,CAAA;IAC5G,CAAC;IAED,uDAAuD;IACvD,uBAAuB,CACrB,aAA4B,EAC5B,GAAW,EACX,IAAkB,EAClB,eAAe,GAAG,IAAI,EACtB,WAAoB,EACpB,WAAW,GAAG,mCAAmC;QAEjD,OAAO,+BAA+B,CAAC,aAAa,EAAE,GAAG,EAAE,CACzD,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,GAAG,EAAE,eAAe,IAAI,IAAI,KAAK,IAAA,oCAAqB,EAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CACzH,CAAA;IACH,CAAC;IAED,yBAAyB,CACvB,qBAAsD,EACtD,GAAW,EACX,IAAkB,EAClB,cAAuB,EACvB,eAAe,GAAG,IAAI,EACtB,WAAoB;QAEpB,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,cAAc,CAAC,CAAA;QACnG,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,YAAY,IAAI,eAAe,IAAI,IAAI,KAAK,IAAA,oCAAqB,EAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IAC9I,CAAC;IAED,qBAAqB,CAAC,qBAAsD,EAAE,cAAkC;QAC9G,MAAM,oBAAoB,GAAG,CAAA,qBAAqB,aAArB,qBAAqB,uBAArB,qBAAqB,CAAE,YAAY,KAAI,IAAI,CAAC,4BAA4B,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAA;QAC9I,OAAO;YACL,YAAY,EAAE,CAAC,CAAC,oBAAoB;YACpC,OAAO,EAAE,oBAAoB,IAAI,cAAc,IAAI,0CAA0C;SAC9F,CAAA;IACH,CAAC;IAED,+BAA+B,CAAC,qBAAsD,EAAE,GAAW,EAAE,IAAkB;QACrH,uDAAuD;QACvD,OAAO,IAAI,CAAC,yBAAyB,CAAC,qBAAqB,EAAE,GAAG,EAAE,IAAI,EAAE,0CAA0C,EAAE,IAAI,CAAC,CAAA;IAC3H,CAAC;IAEO,mBAAmB,CAAC,OAAY,EAAE,GAAW,EAAE,IAAoB;QACzE,MAAM,QAAQ,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,kCAAmB,EAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QACrE,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE;YACzC,GAAG;SACJ,CAAC,CAAA;IACJ,CAAC;IAED,WAAW,CAAC,OAAe,EAAE,IAAoB,EAAE,QAAa,EAAE,EAAE,sBAAsB,GAAG,IAAI;QAC/F,OAAO,IAAA,2BAAa,EAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,GAAG,KAAK,EAAE,EAAE,sBAAsB,CAAC,CAAA;IAClI,CAAC;IAED,aAAa,CAAC,GAAkB,EAAE,UAA4B,EAAE,UAAmB;QACjF,MAAM,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAA;QAC3C,MAAM,SAAS,GAAG,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QAC3C,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,UAAU,EAAE,GAAG,MAAM,EAAE,CAAA;IAC/K,CAAC;IAED,WAAW,CAAC,MAAc;QACxB,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC,CAAA;IACzD,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAA,sBAAO,EAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,IAAA,sBAAO,EAAC,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,CAAC,CAAC,CAAA;IAClH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,MAAwB,EAAE,GAAG,KAAoB;QACjE,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAA;QAChD,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAA;YAC5C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChC,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;gBACtC,CAAC;YACH,CAAC;QACH,CAAC;aAAM,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC,IAAA,8BAAe,EAAC,MAAM,CAAC,EAAE,CAAC;YACtD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAA;YAC5C,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;YACxC,CAAC;YAED,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;YAC1C,IAAI,CAAC,MAAM,IAAA,yBAAU,EAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBAClC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;gBACzC,IAAI,CAAC,MAAM,IAAA,yBAAU,EAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;oBAClC,MAAM,IAAI,wCAAyB,CACjC,mCAAmC,MAAM,uBAAuB,YAAY,wCAAwC,IAAI,CAAC,UAAU,IAAI,CACxI,CAAA;gBACH,CAAC;YACH,CAAC;YACD,OAAO,CAAC,CAAA;QACV,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,IAAI,gBAAgB;QAClB,MAAM,wBAAwB,GAAG,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,CAAA;QACnF,OAAO,CAAC,wBAAwB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,wBAAwB,CAAC,IAAI,KAAK,CAAA;IAC9G,CAAC;IAES,KAAK,CAAC,gBAAgB,CAAC,MAAkB;QACjD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAA,sBAAO,EAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,CAAA;QACtH,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;YACrC,IAAI,SAAS,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC;gBACrC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YAChD,CAAC;YAED,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,6BAA6B,EAAE,EAAE,WAAW,qBAAqB,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;YACpH,OAAO,IAAI,CAAC,uBAAuB,EAAE,CAAA;QACvC,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;QACvB,CAAC;IACH,CAAC;IAED,uBAAuB;QACrB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;QACrC,OAAO,SAAS,CAAC,cAAc,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC1F,CAAC;IAED,mJAAmJ;IACnJ,KAAK,CAAC,WAAW,CAAC,OAAsB,EAAE,eAA8B,EAAE,YAAwB;QAChG,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAY,CAAC,MAAO,CAAC,CAAA;QACjE,MAAM,IAAI,GAAG;YACX,MAAM;YACN,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,IAAI,CAAC,iBAAiB;YACtB,QAAQ;YACR,IAAI,CAAC,UAAU;YACf,OAAO;YACP,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,YAAY,EAAE,CAAC;SAC/D,CAAA;QACD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;QAC9B,CAAC;QACD,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAA;QACvC,CAAC;QAED,MAAM,MAAM,GAAsB,MAAM,IAAA,oCAAuB,EAAC,IAAI,CAAC,CAAA;QACrE,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAA;QACjC,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,MAAM,IAAI,wCAAyB,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,CAAA;QACrE,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,6BAA6B,EAAE,EAAE,WAAW,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;QAChI,CAAC;QAED,OAAO,MAAM,CAAC,KAAK,IAAI,EAAE,CAAA;IAC3B,CAAC;CACF;AAzwBD,4CAywBC;AAiBD,SAAgB,gBAAgB,CAAC,IAAY;IAC3C,OAAO,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AACvC,CAAC;AAED,SAAgB,+BAA+B,CAAC,aAA4B,EAAE,gBAA8B;IAC1G,0DAA0D;IAC1D,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;QAC1B,IAAI,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC;YACpC,OAAO,IAAI,CAAA;QACb,CAAC;QAED,mGAAmG;QACnG,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QAChD,IAAI,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC;YACpC,OAAO,aAAa,CAAA;QACtB,CAAC;IACH,CAAC;IAED,OAAO,gBAAgB,EAAE,CAAA;AAC3B,CAAC;AAED,qBAAqB;AACrB,SAAgB,YAAY,CAAC,GAAW;IACtC,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;AACrD,CAAC;AAED,SAAgB,aAAa,CAAI,EAAe,EAAE,EAAe;IAC/D,OAAO,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;AAC7B,CAAC;AAED,SAAS,qBAAqB,CAAC,IAAY;IACzC,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;AACrD,CAAC", "sourcesContent": ["import { flipFuses, FuseConfig, FuseV1Config, FuseV1Options, FuseVersion } from \"@electron/fuses\"\nimport {\n  Arch,\n  asArray,\n  AsyncTaskManager,\n  DebugLogger,\n  deepAssign,\n  defaultArchFromString,\n  FileTransformer,\n  getArchSuffix,\n  getArtifactArchName,\n  InvalidConfigurationError,\n  isEmptyOrSpaces,\n  log,\n  orIfFileNotExist,\n  statOrNull,\n} from \"builder-util\"\nimport { Nullish } from \"builder-util-runtime\"\nimport { readdir } from \"fs/promises\"\nimport { Lazy } from \"lazy-val\"\nimport { Minimatch } from \"minimatch\"\nimport * as path from \"path\"\nimport { AppInfo } from \"./appInfo\"\nimport { checkFileInArchive } from \"./asar/asarFileChecker\"\nimport { AsarPackager } from \"./asar/asarUtil\"\nimport { AsarIntegrity, computeData } from \"./asar/integrity\"\nimport { FuseOptionsV1 } from \"./configuration\"\nimport { copyFiles, FileMatcher, getFileMatchers, GetFileMatchersOptions, getMainFileMatchers, getNodeModuleFileMatcher } from \"./fileMatcher\"\nimport { createTransformer, isElectronCompileUsed } from \"./fileTransformer\"\nimport { Framework, isElectronBased } from \"./Framework\"\nimport {\n  AfterPackContext,\n  AsarOptions,\n  CompressionLevel,\n  Configuration,\n  ElectronPlatformName,\n  FileAssociation,\n  LinuxPackager,\n  Packager,\n  PackagerOptions,\n  Platform,\n  PlatformSpecificBuildOptions,\n  Target,\n  TargetSpecificOptions,\n} from \"./index\"\nimport { executeAppBuilderAsJson } from \"./util/appBuilder\"\nimport { computeFileSets, computeNodeModuleFileSets, copyAppFiles, ELECTRON_COMPILE_SHIM_FILENAME, transformFiles } from \"./util/appFileCopier\"\nimport { expandMacro as doExpandMacro } from \"./util/macroExpander\"\n\nexport type DoPackOptions<DC extends PlatformSpecificBuildOptions> = {\n  outDir: string\n  appOutDir: string\n  platformName: ElectronPlatformName\n  arch: Arch\n  platformSpecificBuildOptions: DC\n  targets: Array<Target>\n  options?: {\n    sign?: boolean\n    disableAsarIntegrity?: boolean\n    disableFuses?: boolean\n  }\n}\n\nexport abstract class PlatformPackager<DC extends PlatformSpecificBuildOptions> {\n  get packagerOptions(): PackagerOptions {\n    return this.info.options\n  }\n\n  get buildResourcesDir(): string {\n    return this.info.buildResourcesDir\n  }\n\n  get projectDir(): string {\n    return this.info.projectDir\n  }\n\n  get config(): Configuration {\n    return this.info.config\n  }\n\n  readonly platformSpecificBuildOptions: DC\n\n  get resourceList(): Promise<Array<string>> {\n    return this._resourceList.value\n  }\n\n  private readonly _resourceList = new Lazy<Array<string>>(() => orIfFileNotExist(readdir(this.info.buildResourcesDir), []))\n\n  readonly appInfo: AppInfo\n\n  protected constructor(\n    readonly info: Packager,\n    readonly platform: Platform\n  ) {\n    this.platformSpecificBuildOptions = PlatformPackager.normalizePlatformSpecificBuildOptions((this.config as any)[platform.buildConfigurationKey])\n    this.appInfo = this.prepareAppInfo(info.appInfo)\n  }\n\n  get compression(): CompressionLevel {\n    const compression = this.platformSpecificBuildOptions.compression\n    // explicitly set to null - request to use default value instead of parent (in the config)\n    if (compression === null) {\n      return \"normal\"\n    }\n    return compression || this.config.compression || \"normal\"\n  }\n\n  get debugLogger(): DebugLogger {\n    return this.info.debugLogger\n  }\n\n  abstract get defaultTarget(): Array<string>\n\n  // eslint-disable-next-line\n  protected prepareAppInfo(appInfo: AppInfo) {\n    return new AppInfo(this.info, null, this.platformSpecificBuildOptions)\n  }\n\n  private static normalizePlatformSpecificBuildOptions(options: any | Nullish): any {\n    return options == null ? Object.create(null) : options\n  }\n\n  abstract createTargets(targets: Array<string>, mapper: (name: string, factory: (outDir: string) => Target) => void): void\n\n  getCscPassword(): string {\n    const password = this.doGetCscPassword()\n    if (isEmptyOrSpaces(password)) {\n      log.info({ reason: \"CSC_KEY_PASSWORD is not defined\" }, \"empty password will be used for code signing\")\n      return \"\"\n    } else {\n      return password.trim()\n    }\n  }\n\n  getCscLink(extraEnvName?: string | null): string | Nullish {\n    // allow to specify as empty string\n    const envValue = chooseNotNull(extraEnvName == null ? null : process.env[extraEnvName], process.env.CSC_LINK)\n    return chooseNotNull(chooseNotNull(this.info.config.cscLink, this.platformSpecificBuildOptions.cscLink), envValue)\n  }\n\n  doGetCscPassword(): string | Nullish {\n    // allow to specify as empty string\n    return chooseNotNull(chooseNotNull(this.info.config.cscKeyPassword, this.platformSpecificBuildOptions.cscKeyPassword), process.env.CSC_KEY_PASSWORD)\n  }\n\n  protected computeAppOutDir(outDir: string, arch: Arch): string {\n    return (\n      this.packagerOptions.prepackaged ||\n      path.join(\n        outDir,\n        `${this.platform.buildConfigurationKey}${getArchSuffix(arch, this.platformSpecificBuildOptions.defaultArch)}${this.platform === Platform.MAC ? \"\" : \"-unpacked\"}`\n      )\n    )\n  }\n\n  dispatchArtifactCreated(file: string, target: Target | null, arch: Arch | null, safeArtifactName?: string | null): Promise<void> {\n    return this.info.emitArtifactBuildCompleted({\n      file,\n      safeArtifactName,\n      target,\n      arch,\n      packager: this,\n    })\n  }\n\n  async pack(outDir: string, arch: Arch, targets: Array<Target>, taskManager: AsyncTaskManager): Promise<any> {\n    const appOutDir = this.computeAppOutDir(outDir, arch)\n    await this.doPack({\n      outDir,\n      appOutDir,\n      platformName: this.platform.nodeName as ElectronPlatformName,\n      arch,\n      platformSpecificBuildOptions: this.platformSpecificBuildOptions,\n      targets,\n    })\n    this.packageInDistributableFormat(appOutDir, arch, targets, taskManager)\n  }\n\n  protected packageInDistributableFormat(appOutDir: string, arch: Arch, targets: Array<Target>, taskManager: AsyncTaskManager): void {\n    if (targets.find(it => !it.isAsyncSupported) == null) {\n      PlatformPackager.buildAsyncTargets(targets, taskManager, appOutDir, arch)\n      return\n    }\n\n    taskManager.add(async () => {\n      // BluebirdPromise.map doesn't invoke target.build immediately, but for RemoteTarget it is very critical to call build() before finishBuild()\n      const subTaskManager = new AsyncTaskManager(this.info.cancellationToken)\n      PlatformPackager.buildAsyncTargets(targets, subTaskManager, appOutDir, arch)\n      await subTaskManager.awaitTasks()\n\n      for (const target of targets) {\n        if (!target.isAsyncSupported && !this.info.cancellationToken.cancelled) {\n          await target.build(appOutDir, arch)\n        }\n      }\n    })\n  }\n\n  private static buildAsyncTargets(targets: Array<Target>, taskManager: AsyncTaskManager, appOutDir: string, arch: Arch) {\n    for (const target of targets) {\n      if (target.isAsyncSupported) {\n        taskManager.addTask(target.build(appOutDir, arch))\n      }\n    }\n  }\n\n  private getExtraFileMatchers(isResources: boolean, appOutDir: string, options: GetFileMatchersOptions): Array<FileMatcher> | null {\n    const base = isResources\n      ? this.getResourcesDir(appOutDir)\n      : this.platform === Platform.MAC\n        ? path.join(appOutDir, `${this.appInfo.productFilename}.app`, \"Contents\")\n        : appOutDir\n    return getFileMatchers(this.config, isResources ? \"extraResources\" : \"extraFiles\", base, options)\n  }\n\n  createGetFileMatchersOptions(outDir: string, arch: Arch, customBuildOptions: PlatformSpecificBuildOptions): GetFileMatchersOptions {\n    return {\n      macroExpander: it => this.expandMacro(it, arch == null ? null : Arch[arch], { \"/*\": \"{,/**/*}\" }),\n      customBuildOptions,\n      globalOutDir: outDir,\n      defaultSrc: this.projectDir,\n    }\n  }\n\n  protected async doPack(packOptions: DoPackOptions<DC>) {\n    if (this.packagerOptions.prepackaged != null) {\n      return\n    }\n\n    if (this.info.cancellationToken.cancelled) {\n      return\n    }\n\n    // Due to node-gyp rewriting GYP_MSVS_VERSION when reused across the same session, we must reset the env var: https://github.com/electron-userland/electron-builder/issues/7256\n    delete process.env.GYP_MSVS_VERSION\n\n    const { outDir, appOutDir, platformName, arch, platformSpecificBuildOptions, targets, options } = packOptions\n\n    await this.info.emitBeforePack({\n      appOutDir,\n      outDir,\n      arch,\n      targets,\n      packager: this,\n      electronPlatformName: platformName,\n    })\n\n    await this.info.installAppDependencies(this.platform, arch)\n\n    if (this.info.cancellationToken.cancelled) {\n      return\n    }\n\n    const framework = this.info.framework\n    log.info(\n      {\n        platform: platformName,\n        arch: Arch[arch],\n        [`${framework.name}`]: framework.version,\n        appOutDir: log.filePath(appOutDir),\n      },\n      `packaging`\n    )\n\n    await framework.prepareApplicationStageDirectory({\n      packager: this,\n      appOutDir,\n      platformName,\n      arch: Arch[arch],\n      version: framework.version,\n    })\n\n    await this.info.emitAfterExtract({\n      appOutDir,\n      outDir,\n      arch,\n      targets,\n      packager: this,\n      electronPlatformName: platformName,\n    })\n\n    const excludePatterns: Array<Minimatch> = []\n\n    const computeParsedPatterns = (patterns: Array<FileMatcher> | null) => {\n      if (patterns != null) {\n        for (const pattern of patterns) {\n          pattern.computeParsedPatterns(excludePatterns, this.info.projectDir)\n        }\n      }\n    }\n\n    const getFileMatchersOptions = this.createGetFileMatchersOptions(outDir, arch, platformSpecificBuildOptions)\n    const macroExpander = getFileMatchersOptions.macroExpander\n    const extraResourceMatchers = this.getExtraFileMatchers(true, appOutDir, getFileMatchersOptions)\n    computeParsedPatterns(extraResourceMatchers)\n    const extraFileMatchers = this.getExtraFileMatchers(false, appOutDir, getFileMatchersOptions)\n    computeParsedPatterns(extraFileMatchers)\n\n    const packContext: AfterPackContext = {\n      appOutDir,\n      outDir,\n      arch,\n      targets,\n      packager: this,\n      electronPlatformName: platformName,\n    }\n\n    const asarOptions = await this.computeAsarOptions(platformSpecificBuildOptions)\n    const resourcesPath =\n      this.platform === Platform.MAC\n        ? path.join(appOutDir, framework.distMacOsAppName, \"Contents\", \"Resources\")\n        : isElectronBased(framework)\n          ? path.join(appOutDir, \"resources\")\n          : appOutDir\n    const taskManager = new AsyncTaskManager(this.info.cancellationToken)\n    this.copyAppFiles(taskManager, asarOptions, resourcesPath, path.join(resourcesPath, \"app\"), packContext, platformSpecificBuildOptions, excludePatterns, macroExpander)\n    await taskManager.awaitTasks()\n\n    if (this.info.cancellationToken.cancelled) {\n      return\n    }\n\n    if (framework.beforeCopyExtraFiles != null) {\n      const resourcesRelativePath = this.platform === Platform.MAC ? \"Resources\" : isElectronBased(framework) ? \"resources\" : \"\"\n\n      let asarIntegrity: AsarIntegrity | null = null\n      if (!(asarOptions == null || options?.disableAsarIntegrity)) {\n        asarIntegrity = await computeData({ resourcesPath, resourcesRelativePath, resourcesDestinationPath: this.getResourcesDir(appOutDir), extraResourceMatchers })\n      }\n\n      await framework.beforeCopyExtraFiles({\n        packager: this,\n        appOutDir,\n        asarIntegrity,\n        platformName,\n      })\n    }\n\n    if (this.info.cancellationToken.cancelled) {\n      return\n    }\n\n    const transformerForExtraFiles = this.createTransformerForExtraFiles(packContext)\n    await copyFiles(extraResourceMatchers, transformerForExtraFiles)\n    await copyFiles(extraFileMatchers, transformerForExtraFiles)\n\n    if (this.info.cancellationToken.cancelled) {\n      return\n    }\n\n    await this.info.emitAfterPack(packContext)\n\n    if (framework.afterPack != null) {\n      await framework.afterPack(packContext)\n    }\n\n    const isAsar = asarOptions != null\n    await this.sanityCheckPackage(appOutDir, isAsar, framework, !!this.config.disableSanityCheckAsar)\n\n    if (!options?.disableFuses) {\n      await this.doAddElectronFuses(packContext)\n    }\n    if (options?.sign ?? true) {\n      await this.doSignAfterPack(outDir, appOutDir, platformName, arch, platformSpecificBuildOptions, targets)\n    }\n  }\n\n  // the fuses MUST be flipped right before signing\n  protected async doAddElectronFuses(packContext: AfterPackContext) {\n    if (this.config.electronFuses == null) {\n      return\n    }\n    const fuseConfig = this.generateFuseConfig(this.config.electronFuses)\n    await this.addElectronFuses(packContext, fuseConfig)\n  }\n\n  private generateFuseConfig(fuses: FuseOptionsV1): FuseV1Config {\n    const config: FuseV1Config = {\n      version: FuseVersion.V1,\n      resetAdHocDarwinSignature: fuses.resetAdHocDarwinSignature,\n    }\n    // this is annoying, but we must filter out undefined entries because some older electron versions will receive `the fuse wire in this version of Electron is not long enough` even if entry is set undefined\n    if (fuses.runAsNode != null) {\n      config[FuseV1Options.RunAsNode] = fuses.runAsNode\n    }\n    if (fuses.enableCookieEncryption != null) {\n      config[FuseV1Options.EnableCookieEncryption] = fuses.enableCookieEncryption\n    }\n    if (fuses.enableNodeOptionsEnvironmentVariable != null) {\n      config[FuseV1Options.EnableNodeOptionsEnvironmentVariable] = fuses.enableNodeOptionsEnvironmentVariable\n    }\n    if (fuses.enableNodeCliInspectArguments != null) {\n      config[FuseV1Options.EnableNodeCliInspectArguments] = fuses.enableNodeCliInspectArguments\n    }\n    if (fuses.enableEmbeddedAsarIntegrityValidation != null) {\n      config[FuseV1Options.EnableEmbeddedAsarIntegrityValidation] = fuses.enableEmbeddedAsarIntegrityValidation\n    }\n    if (fuses.onlyLoadAppFromAsar != null) {\n      config[FuseV1Options.OnlyLoadAppFromAsar] = fuses.onlyLoadAppFromAsar\n    }\n    if (fuses.loadBrowserProcessSpecificV8Snapshot != null) {\n      config[FuseV1Options.LoadBrowserProcessSpecificV8Snapshot] = fuses.loadBrowserProcessSpecificV8Snapshot\n    }\n    if (fuses.grantFileProtocolExtraPrivileges != null) {\n      config[FuseV1Options.GrantFileProtocolExtraPrivileges] = fuses.grantFileProtocolExtraPrivileges\n    }\n    return config\n  }\n\n  /**\n   * Use `AfterPackContext` here to keep available for public API\n   * @param {AfterPackContext} context\n   * @param {FuseConfig} fuses\n   *\n   * Can be used in `afterPack` hook for custom fuse logic like below. It's an alternative approach if one wants to override electron-builder's @electron/fuses version\n   * ```\n   * await context.packager.addElectronFuses(context, { ... })\n   * ```\n   */\n  public addElectronFuses(context: AfterPackContext, fuses: FuseConfig) {\n    const { appOutDir, electronPlatformName } = context\n\n    const ext = {\n      darwin: \".app\",\n      mas: \".app\",\n      win32: \".exe\",\n      linux: \"\",\n    }[electronPlatformName]\n\n    const executableName = this instanceof LinuxPackager ? this.executableName : this.appInfo.productFilename\n    const electronBinaryPath = path.join(appOutDir, `${executableName}${ext}`)\n\n    log.info({ electronPath: log.filePath(electronBinaryPath) }, \"executing @electron/fuses\")\n    return flipFuses(electronBinaryPath, fuses)\n  }\n\n  protected async doSignAfterPack(outDir: string, appOutDir: string, platformName: ElectronPlatformName, arch: Arch, platformSpecificBuildOptions: DC, targets: Array<Target>) {\n    const asarOptions = await this.computeAsarOptions(platformSpecificBuildOptions)\n    const isAsar = asarOptions != null\n    const packContext = {\n      appOutDir,\n      outDir,\n      arch,\n      targets,\n      packager: this,\n      electronPlatformName: platformName,\n    }\n    const didSign = await this.signApp(packContext, isAsar)\n    if (didSign) {\n      await this.info.emitAfterSign(packContext)\n    } else if (this.info.filterPackagerEventListeners(\"afterSign\", \"user\").length) {\n      log.warn(null, `skipping \"afterSign\" hook as no signing occurred, perhaps you intended \"afterPack\"?`)\n    }\n  }\n\n  // eslint-disable-next-line\n  protected createTransformerForExtraFiles(packContext: AfterPackContext): FileTransformer | null {\n    return null\n  }\n\n  private copyAppFiles(\n    taskManager: AsyncTaskManager,\n    asarOptions: AsarOptions | null,\n    resourcePath: string,\n    defaultDestination: string,\n    packContext: AfterPackContext,\n    platformSpecificBuildOptions: DC,\n    excludePatterns: Array<Minimatch>,\n    macroExpander: (it: string) => string\n  ) {\n    const appDir = this.info.appDir\n    const config = this.config\n    const isElectronCompile = asarOptions != null && isElectronCompileUsed(this.info)\n\n    const mainMatchers = getMainFileMatchers(appDir, defaultDestination, macroExpander, platformSpecificBuildOptions, this, packContext.outDir, isElectronCompile)\n    if (excludePatterns.length > 0) {\n      for (const matcher of mainMatchers) {\n        matcher.excludePatterns = excludePatterns\n      }\n    }\n\n    const framework = this.info.framework\n    const transformer = createTransformer(\n      appDir,\n      config,\n      isElectronCompile\n        ? {\n            originalMain: this.info.metadata.main,\n            main: ELECTRON_COMPILE_SHIM_FILENAME,\n            ...config.extraMetadata,\n          }\n        : config.extraMetadata,\n      framework.createTransformer == null ? null : framework.createTransformer()\n    )\n\n    const _computeFileSets = (matchers: Array<FileMatcher>) => {\n      return computeFileSets(matchers, this.info.isPrepackedAppAsar ? null : transformer, this, isElectronCompile).then(async result => {\n        if (!this.info.isPrepackedAppAsar && !this.info.areNodeModulesHandledExternally) {\n          const moduleFileMatcher = getNodeModuleFileMatcher(appDir, defaultDestination, macroExpander, platformSpecificBuildOptions, this.info)\n          result = result.concat(await computeNodeModuleFileSets(this, moduleFileMatcher))\n        }\n        return result.filter(it => it.files.length > 0)\n      })\n    }\n\n    if (this.info.isPrepackedAppAsar) {\n      taskManager.add(async () => {\n        const fileSets = await _computeFileSets([new FileMatcher(appDir, resourcePath, macroExpander)])\n        fileSets.forEach(it => taskManager.addTask(copyAppFiles(it, this.info, transformer)))\n        await taskManager.awaitTasks()\n      })\n    } else if (asarOptions == null) {\n      // for ASAR all asar unpacked files will be extra transformed (e.g. sign of EXE and DLL) later,\n      // for prepackaged asar extra transformation not supported yet,\n      // so, extra transform if asar is disabled\n      const transformerForExtraFiles = this.createTransformerForExtraFiles(packContext)\n      const combinedTransformer: FileTransformer = file => {\n        if (transformerForExtraFiles != null) {\n          const result = transformerForExtraFiles(file)\n          if (result != null) {\n            return result\n          }\n        }\n        return transformer(file)\n      }\n      taskManager.add(async () => {\n        const fileSets = await _computeFileSets(mainMatchers)\n        fileSets.forEach(it => taskManager.addTask(copyAppFiles(it, this.info, combinedTransformer)))\n        await taskManager.awaitTasks()\n      })\n    } else {\n      const unpackPattern = getFileMatchers(config, \"asarUnpack\", defaultDestination, {\n        macroExpander,\n        customBuildOptions: platformSpecificBuildOptions,\n        globalOutDir: packContext.outDir,\n        defaultSrc: appDir,\n      })\n      const fileMatcher = unpackPattern == null ? null : unpackPattern[0]\n      taskManager.addTask(\n        _computeFileSets(mainMatchers).then(async fileSets => {\n          for (const fileSet of fileSets) {\n            await transformFiles(transformer, fileSet)\n          }\n\n          await new AsarPackager(this, {\n            defaultDestination,\n            resourcePath,\n            options: asarOptions,\n            unpackPattern: fileMatcher?.createFilter(),\n          }).pack(fileSets)\n        })\n      )\n    }\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  protected signApp(packContext: AfterPackContext, isAsar: boolean): Promise<boolean> {\n    return Promise.resolve(false)\n  }\n\n  getIconPath(): Promise<string | null> {\n    return Promise.resolve(null)\n  }\n\n  private async computeAsarOptions(customBuildOptions: DC): Promise<AsarOptions | null> {\n    if (!isElectronBased(this.info.framework)) {\n      return null\n    }\n\n    function errorMessage(name: string) {\n      return `${name} is deprecated is deprecated and not supported — please use asarUnpack`\n    }\n\n    const buildMetadata = this.config as any\n    if (buildMetadata[\"asar-unpack\"] != null) {\n      throw new Error(errorMessage(\"asar-unpack\"))\n    }\n    if (buildMetadata[\"asar-unpack-dir\"] != null) {\n      throw new Error(errorMessage(\"asar-unpack-dir\"))\n    }\n\n    const platformSpecific = customBuildOptions.asar\n    const result = platformSpecific == null ? this.config.asar : platformSpecific\n    if (result === false) {\n      const appAsarStat = await statOrNull(path.join(this.info.appDir, \"app.asar\"))\n      //noinspection ES6MissingAwait\n      if (appAsarStat == null || !appAsarStat.isFile()) {\n        log.warn(\n          {\n            solution: \"enable asar and use asarUnpack to unpack files that must be externally available\",\n          },\n          \"asar usage is disabled — this is strongly not recommended\"\n        )\n      }\n      return null\n    }\n\n    if (result == null || result === true) {\n      return {}\n    }\n\n    for (const name of [\"unpackDir\", \"unpack\"]) {\n      if ((result as any)[name] != null) {\n        throw new Error(errorMessage(`asar.${name}`))\n      }\n    }\n    return deepAssign({}, result)\n  }\n\n  public getElectronSrcDir(dist: string): string {\n    return path.resolve(this.projectDir, dist)\n  }\n\n  public getElectronDestinationDir(appOutDir: string): string {\n    return appOutDir\n  }\n\n  getResourcesDir(appOutDir: string): string {\n    if (this.platform === Platform.MAC) {\n      return this.getMacOsResourcesDir(appOutDir)\n    } else if (isElectronBased(this.info.framework)) {\n      return path.join(appOutDir, \"resources\")\n    } else {\n      return appOutDir\n    }\n  }\n\n  public getMacOsResourcesDir(appOutDir: string): string {\n    return path.join(appOutDir, `${this.appInfo.productFilename}.app`, \"Contents\", \"Resources\")\n  }\n\n  private async checkFileInPackage(resourcesDir: string, file: string, messagePrefix: string, isAsar: boolean, disableSanityCheckAsar: boolean) {\n    if (isAsar && disableSanityCheckAsar) {\n      return\n    }\n    const relativeFile = path.relative(this.info.appDir, path.resolve(this.info.appDir, file))\n    if (isAsar) {\n      checkFileInArchive(path.join(resourcesDir, \"app.asar\"), relativeFile, messagePrefix)\n      return\n    }\n\n    const pathParsed = path.parse(file)\n    // Even when packaging to asar is disabled, it does not imply that the main file can not be inside an .asar archive.\n    // This may occur when the packaging is done manually before processing with electron-builder.\n    if (pathParsed.dir.includes(\".asar\")) {\n      // The path needs to be split to the part with an asar archive which acts like a directory and the part with\n      // the path to main file itself. (e.g. path/arch.asar/dir/index.js -> path/arch.asar, dir/index.js)\n      // noinspection TypeScriptValidateJSTypes\n      const pathSplit: Array<string> = pathParsed.dir.split(path.sep)\n      let partWithAsarIndex = 0\n      pathSplit.some((pathPart: string, index: number) => {\n        partWithAsarIndex = index\n        return pathPart.endsWith(\".asar\")\n      })\n      const asarPath = path.join(...pathSplit.slice(0, partWithAsarIndex + 1))\n      let mainPath = pathSplit.length > partWithAsarIndex + 1 ? path.join.apply(pathSplit.slice(partWithAsarIndex + 1)) : \"\"\n      mainPath += path.join(mainPath, pathParsed.base)\n      checkFileInArchive(path.join(resourcesDir, \"app\", asarPath), mainPath, messagePrefix)\n    } else {\n      const fullPath = path.join(resourcesDir, \"app\", relativeFile)\n      const outStat = await statOrNull(fullPath)\n      if (outStat == null) {\n        throw new Error(`${messagePrefix} \"${fullPath}\" does not exist. Seems like a wrong configuration.`)\n      } else {\n        //noinspection ES6MissingAwait\n        if (!outStat.isFile()) {\n          throw new Error(`${messagePrefix} \"${fullPath}\" is not a file. Seems like a wrong configuration.`)\n        }\n      }\n    }\n  }\n\n  private async sanityCheckPackage(appOutDir: string, isAsar: boolean, framework: Framework, disableSanityCheckAsar: boolean): Promise<any> {\n    const outStat = await statOrNull(appOutDir)\n    if (outStat == null) {\n      throw new Error(`Output directory \"${appOutDir}\" does not exist. Seems like a wrong configuration.`)\n    } else {\n      //noinspection ES6MissingAwait\n      if (!outStat.isDirectory()) {\n        throw new Error(`Output directory \"${appOutDir}\" is not a directory. Seems like a wrong configuration.`)\n      }\n    }\n\n    const resourcesDir = this.getResourcesDir(appOutDir)\n    const mainFile = (framework.getMainFile == null ? null : framework.getMainFile(this.platform)) || this.info.metadata.main || \"index.js\"\n    await this.checkFileInPackage(resourcesDir, mainFile, \"Application entry file\", isAsar, disableSanityCheckAsar)\n    await this.checkFileInPackage(resourcesDir, \"package.json\", \"Application\", isAsar, disableSanityCheckAsar)\n  }\n\n  // tslint:disable-next-line:no-invalid-template-strings\n  computeSafeArtifactName(\n    suggestedName: string | null,\n    ext: string,\n    arch?: Arch | null,\n    skipDefaultArch = true,\n    defaultArch?: string,\n    safePattern = \"${name}-${version}-${arch}.${ext}\"\n  ): string | null {\n    return computeSafeArtifactNameIfNeeded(suggestedName, () =>\n      this.computeArtifactName(safePattern, ext, skipDefaultArch && arch === defaultArchFromString(defaultArch) ? null : arch)\n    )\n  }\n\n  expandArtifactNamePattern(\n    targetSpecificOptions: TargetSpecificOptions | Nullish,\n    ext: string,\n    arch?: Arch | null,\n    defaultPattern?: string,\n    skipDefaultArch = true,\n    defaultArch?: string\n  ): string {\n    const { pattern, isUserForced } = this.artifactPatternConfig(targetSpecificOptions, defaultPattern)\n    return this.computeArtifactName(pattern, ext, !isUserForced && skipDefaultArch && arch === defaultArchFromString(defaultArch) ? null : arch)\n  }\n\n  artifactPatternConfig(targetSpecificOptions: TargetSpecificOptions | Nullish, defaultPattern: string | undefined) {\n    const userSpecifiedPattern = targetSpecificOptions?.artifactName || this.platformSpecificBuildOptions.artifactName || this.config.artifactName\n    return {\n      isUserForced: !!userSpecifiedPattern,\n      pattern: userSpecifiedPattern || defaultPattern || \"${productName}-${version}-${arch}.${ext}\",\n    }\n  }\n\n  expandArtifactBeautyNamePattern(targetSpecificOptions: TargetSpecificOptions | Nullish, ext: string, arch?: Arch | null): string {\n    // tslint:disable-next-line:no-invalid-template-strings\n    return this.expandArtifactNamePattern(targetSpecificOptions, ext, arch, \"${productName} ${version} ${arch}.${ext}\", true)\n  }\n\n  private computeArtifactName(pattern: any, ext: string, arch: Arch | Nullish): string {\n    const archName = arch == null ? null : getArtifactArchName(arch, ext)\n    return this.expandMacro(pattern, archName, {\n      ext,\n    })\n  }\n\n  expandMacro(pattern: string, arch?: string | null, extra: any = {}, isProductNameSanitized = true): string {\n    return doExpandMacro(pattern, arch, this.appInfo, { os: this.platform.buildConfigurationKey, ...extra }, isProductNameSanitized)\n  }\n\n  generateName2(ext: string | null, classifier: string | Nullish, deployment: boolean): string {\n    const dotExt = ext == null ? \"\" : `.${ext}`\n    const separator = ext === \"deb\" ? \"_\" : \"-\"\n    return `${deployment ? this.appInfo.name : this.appInfo.productFilename}${separator}${this.appInfo.version}${classifier == null ? \"\" : `${separator}${classifier}`}${dotExt}`\n  }\n\n  getTempFile(suffix: string): Promise<string> {\n    return this.info.tempDirManager.getTempFile({ suffix })\n  }\n\n  get fileAssociations(): Array<FileAssociation> {\n    return asArray(this.config.fileAssociations).concat(asArray(this.platformSpecificBuildOptions.fileAssociations))\n  }\n\n  async getResource(custom: string | Nullish, ...names: Array<string>): Promise<string | null> {\n    const resourcesDir = this.info.buildResourcesDir\n    if (custom === undefined) {\n      const resourceList = await this.resourceList\n      for (const name of names) {\n        if (resourceList.includes(name)) {\n          return path.join(resourcesDir, name)\n        }\n      }\n    } else if (custom != null && !isEmptyOrSpaces(custom)) {\n      const resourceList = await this.resourceList\n      if (resourceList.includes(custom)) {\n        return path.join(resourcesDir, custom)\n      }\n\n      let p = path.resolve(resourcesDir, custom)\n      if ((await statOrNull(p)) == null) {\n        p = path.resolve(this.projectDir, custom)\n        if ((await statOrNull(p)) == null) {\n          throw new InvalidConfigurationError(\n            `cannot find specified resource \"${custom}\", nor relative to \"${resourcesDir}\", neither relative to project dir (\"${this.projectDir}\")`\n          )\n        }\n      }\n      return p\n    }\n    return null\n  }\n\n  get forceCodeSigning(): boolean {\n    const forceCodeSigningPlatform = this.platformSpecificBuildOptions.forceCodeSigning\n    return (forceCodeSigningPlatform == null ? this.config.forceCodeSigning : forceCodeSigningPlatform) || false\n  }\n\n  protected async getOrConvertIcon(format: IconFormat): Promise<string | null> {\n    const result = await this.resolveIcon(asArray(this.platformSpecificBuildOptions.icon || this.config.icon), [], format)\n    if (result.length === 0) {\n      const framework = this.info.framework\n      if (framework.getDefaultIcon != null) {\n        return framework.getDefaultIcon(this.platform)\n      }\n\n      log.warn({ reason: \"application icon is not set\" }, `default ${capitalizeFirstLetter(framework.name)} icon is used`)\n      return this.getDefaultFrameworkIcon()\n    } else {\n      return result[0].file\n    }\n  }\n\n  getDefaultFrameworkIcon(): string | null {\n    const framework = this.info.framework\n    return framework.getDefaultIcon == null ? null : framework.getDefaultIcon(this.platform)\n  }\n\n  // convert if need, validate size (it is a reason why tool is called even if file has target extension (already specified as foo.icns for example))\n  async resolveIcon(sources: Array<string>, fallbackSources: Array<string>, outputFormat: IconFormat): Promise<Array<IconInfo>> {\n    const output = this.expandMacro(this.config.directories!.output!)\n    const args = [\n      \"icon\",\n      \"--format\",\n      outputFormat,\n      \"--root\",\n      this.buildResourcesDir,\n      \"--root\",\n      this.projectDir,\n      \"--out\",\n      path.resolve(this.projectDir, output, `.icon-${outputFormat}`),\n    ]\n    for (const source of sources) {\n      args.push(\"--input\", source)\n    }\n    for (const source of fallbackSources) {\n      args.push(\"--fallback-input\", source)\n    }\n\n    const result: IconConvertResult = await executeAppBuilderAsJson(args)\n    const errorMessage = result.error\n    if (errorMessage != null) {\n      throw new InvalidConfigurationError(errorMessage, result.errorCode)\n    }\n\n    if (result.isFallback) {\n      log.warn({ reason: \"application icon is not set\" }, `default ${capitalizeFirstLetter(this.info.framework.name)} icon is used`)\n    }\n\n    return result.icons || []\n  }\n}\n\nexport interface IconInfo {\n  file: string\n  size: number\n}\n\ninterface IconConvertResult {\n  icons?: Array<IconInfo>\n\n  error?: string\n  errorCode?: string\n  isFallback?: boolean\n}\n\nexport type IconFormat = \"icns\" | \"ico\" | \"set\"\n\nexport function isSafeGithubName(name: string) {\n  return /^[0-9A-Za-z._-]+$/.test(name)\n}\n\nexport function computeSafeArtifactNameIfNeeded(suggestedName: string | null, safeNameProducer: () => string): string | null {\n  // GitHub only allows the listed characters in file names.\n  if (suggestedName != null) {\n    if (isSafeGithubName(suggestedName)) {\n      return null\n    }\n\n    // prefer to use suggested name - so, if space is the only problem, just replace only space to dash\n    suggestedName = suggestedName.replace(/ /g, \"-\")\n    if (isSafeGithubName(suggestedName)) {\n      return suggestedName\n    }\n  }\n\n  return safeNameProducer()\n}\n\n// remove leading dot\nexport function normalizeExt(ext: string) {\n  return ext.startsWith(\".\") ? ext.substring(1) : ext\n}\n\nexport function chooseNotNull<T>(v1: T | Nullish, v2: T | Nullish): T | Nullish {\n  return v1 == null ? v2 : v1\n}\n\nfunction capitalizeFirstLetter(text: string) {\n  return text.charAt(0).toUpperCase() + text.slice(1)\n}\n"]}