{"name": "tesseract.js", "version": "6.0.1", "description": "Pure Javascript Multilingual OCR", "main": "src/index.js", "type": "commonjs", "types": "src/index.d.ts", "unpkg": "dist/tesseract.min.js", "jsdelivr": "dist/tesseract.min.js", "scripts": {"start": "node scripts/server.js", "build": "rimraf dist && webpack --config scripts/webpack.config.prod.js && rollup -c scripts/rollup.esm.mjs", "profile:tesseract": "webpack-bundle-analyzer dist/tesseract-stats.json", "profile:worker": "webpack-bundle-analyzer dist/worker-stats.json", "prepublishOnly": "npm run build", "wait": "rimraf dist && wait-on http://localhost:3000/dist/tesseract.min.js", "test": "npm-run-all -p -r start test:all", "test:all": "npm-run-all wait test:browser test:node:all", "test:browser": "karma start karma.conf.js", "test:node": "nyc mocha --exit --bail --require ./scripts/test-helper.mjs", "test:node:all": "npm run test:node -- ./tests/*.test.mjs", "lint": "eslint src", "lint:fix": "eslint --fix src", "postinstall": "opencollective-postinstall || true"}, "browser": {"./src/worker/node/index.js": "./src/worker/browser/index.js"}, "author": "", "contributors": ["j<PERSON><PERSON><PERSON>"], "license": "Apache-2.0", "devDependencies": {"@babel/core": "^7.21.4", "@babel/eslint-parser": "^7.21.3", "@babel/preset-env": "^7.21.4", "@rollup/plugin-commonjs": "^24.1.0", "acorn": "^8.8.2", "babel-loader": "^9.1.2", "buffer": "^6.0.3", "cors": "^2.8.5", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-import": "^2.27.5", "expect.js": "^0.3.1", "express": "^4.18.2", "mocha": "^10.2.0", "npm-run-all": "^4.1.5", "karma": "^6.4.2", "karma-chrome-launcher": "^3.2.0", "karma-firefox-launcher": "^2.1.2", "karma-mocha": "^2.0.1", "karma-webpack": "^5.0.0", "nyc": "^15.1.0", "rimraf": "^5.0.0", "rollup": "^3.20.7", "wait-on": "^7.0.1", "webpack": "^5.79.0", "webpack-bundle-analyzer": "^4.8.0", "webpack-cli": "^5.0.1", "webpack-dev-middleware": "^6.0.2", "rollup-plugin-sourcemaps": "^0.6.3"}, "dependencies": {"bmp-js": "^0.1.0", "idb-keyval": "^6.2.0", "is-url": "^1.2.4", "node-fetch": "^2.6.9", "opencollective-postinstall": "^2.0.3", "regenerator-runtime": "^0.13.3", "tesseract.js-core": "^6.0.0", "wasm-feature-detect": "^1.2.11", "zlibjs": "^0.3.1"}, "overrides": {"@rollup/pluginutils": "^5.0.2"}, "repository": {"type": "git", "url": "https://github.com/naptha/tesseract.js.git"}, "bugs": {"url": "https://github.com/naptha/tesseract.js/issues"}, "homepage": "https://github.com/naptha/tesseract.js", "collective": {"type": "opencollective", "url": "https://opencollective.com/tesseractjs"}}