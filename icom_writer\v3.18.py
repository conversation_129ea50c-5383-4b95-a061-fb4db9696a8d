# CS-F7500 UIA automation - v3.18
# - Reads 'radio' from new_channel.csv, looks up COM in radio_config.csv
# - Sets COM Port in Icom app via menu/dialog before entering each radio's rows
# - Kills existing EX3852A.EXE
# - Absolute-from-Home hops with HARD HOME, F2 for Name, Mode via dropdown (Enter, Down, Enter)
# - Detached launch + robust attach (PID or title)
# - ASCII-only to avoid encoding issues

import os
import time
import csv
import subprocess
from collections import defaultdict
from datetime import datetime
from pywinauto import Desktop
from pywinauto.application import Application
from pywinauto import keyboard

# =========================
# CONFIG / PATHS
# =========================
WORKDIR = r"C:\cc\icom"
EXE_PATH = r"C:\Program Files (x86)\Icom\CS-F7500\EX3852A.EXE"

os.makedirs(WORKDIR, exist_ok=True)
CSV_FILE = os.path.join(WORKDIR, "new_channel.csv")
RADIO_CONFIG_FILE = os.path.join(WORKDIR, "radio_config.csv")
LOG_FILE = os.path.join(WORKDIR, "programming_log.txt")

WINDOW_TITLE_KEYS = ["CS-F7500", "Icom", "EX3852A"]

# Timeouts / waits (seconds)
POST_LAUNCH_WAIT   = 1.5
ATTACH_TIMEOUT     = 60.0
POST_ATTACH_SETTLE = 1.0
POST_MAXIMIZE_WAIT = 1.0
TREE_RETRY_SECONDS = 25
COM_DIALOG_WAIT    = 12.0

# Click inside the grid area to wake focus (adjust if needed)
GRID_NUDGE_POINT = (900, 400)

# Absolute offsets: RIGHT from Home to each column (your working hops)
HOME_TO_COL = {
    "name":  4,
    "mode":  5,
    "rx":    6,   # RX is required
    "tx":    7,
    "rxctc": 9,
    "txctc": 10,
}

# Mode dropdown: Enter, DOWN N, Enter (Analog = 1 down)
MODE_DROPDOWN_DOWN_PRESSES = 1

ALWAYS_SET_TONE_MODE = False
PAUSE_ON_ERROR = True
LOG_TO_FILE = True

# =========================
# LOGGING
# =========================
def log(msg: str):
    ts = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
    line = f"{ts} {msg}"
    print(line)
    if LOG_TO_FILE:
        try:
            with open(LOG_FILE, "a", encoding="utf-8") as f:
                f.write(line + "\n")
        except Exception:
            pass

def pause(label=""):
    if PAUSE_ON_ERROR:
        try:
            input(f"[{label}] Press Enter to continue...")
        except Exception:
            time.sleep(2)

# =========================
# CSV LOADING (enc fallback)
# =========================
def load_csv_with_fallbacks(path):
    encodings = ["utf-8-sig", "utf-8", "cp1252", "latin-1"]
    last_err = None
    for enc in encodings:
        try:
            with open(path, newline="", encoding=enc) as f:
                return list(csv.DictReader(f)), enc
        except Exception as e:
            last_err = e
    raise last_err

def read_radio_config(path):
    # returns dict: radio_id(lower) -> "COMx"
    def normalize_com(s):
        s = (s or "").strip().upper()
        if not s:
            return ""
        if s.startswith("COM"):
            return s
        # accept digits like "7" -> COM7
        if s.isdigit():
            return "COM" + s
        # accept "COM-7"
        return s.replace(" ", "").replace("-", "")
    try:
        dict_rows, used = load_csv_with_fallbacks(path)
        log(f"Loaded radio_config with encoding: {used}")
    except Exception as e:
        log(f"ERROR reading radio_config: {e}")
        return {}
    out = {}
    for r in dict_rows:
        rdradio = (r.get("radio") or r.get("unit") or r.get("device") or r.get("radio_id") or "").strip()
        com = (r.get("comport") or r.get("com_port") or r.get("com") or r.get("port") or "").strip()
        if rdradio:
            out[rdradio.lower()] = normalize_com(com)
    return out

def parse_new_channels(dict_rows):
    # returns list of dicts with keys: radio,name,rx,tx,rxtone,txtone
    out = []
    def get(r, *names):
        for n in names:
            v = r.get(n)
            if v is not None:
                return v.strip()
        return ""
    for r in dict_rows:
        radio  = get(r, "radio", "unit", "device", "radio_id")
        name   = get(r, "name")
        rx     = get(r, "rx")
        tx     = get(r, "tx")
        rxtone = get(r, "rxtone", "rx_ctone")
        txtone = get(r, "txtone", "tx_ctone")
        out.append({
            "radio": radio,
            "name": name,
            "rx": rx,
            "tx": tx,
            "rxtone": rxtone,
            "txtone": txtone,
        })
    return out

# =========================
# PRELAUNCH CLEANUP
# =========================
def kill_existing_processes():
    names = ["EX3852A.EXE"]
    for name in names:
        try:
            r = subprocess.run(
                ["taskkill", "/F", "/IM", name],
                capture_output=True, text=True, shell=False
            )
            out = (r.stdout or "") + (r.stderr or "")
            if r.returncode == 0:
                log(f"Killed existing {name}")
            else:
                low = out.lower()
                if "no instance" in low or "not found" in low:
                    log(f"No running {name} to kill.")
                else:
                    log(f"WARN taskkill {name}: rc={r.returncode} msg='{out.strip()[:200]}'")
        except Exception as e:
            log(f"WARN taskkill {name}: {e}")
    time.sleep(0.8)

# =========================
# WINDOW DISCOVERY / ATTACH
# =========================
def list_top_windows():
    try:
        wins = Desktop(backend="uia").windows()
        for w in wins:
            try:
                log(f"WIN pid={w.process_id} vis={w.is_visible()} title='{w.window_text()}' class='{w.friendly_class_name()}'")
            except Exception:
                pass
    except Exception:
        pass

def try_connect_by_pid(pid):
    try:
        app = Application(backend="uia").connect(process=pid, timeout=5)
        win = app.top_window()
        return app, win
    except Exception:
        return None, None

def find_window_by_title_keys():
    wins = Desktop(backend="uia").windows()
    for w in wins:
        try:
            title = (w.window_text() or "").strip()
            if not title:
                continue
            for key in WINDOW_TITLE_KEYS:
                if key.lower() in title.lower() and w.is_visible():
                    return w
        except Exception:
            continue
    return None

def has_tree_control(win):
    try:
        t = win.child_window(control_type="Tree")
        return bool(t and t.exists())
    except Exception:
        return False

def attach_or_launch():
    log("Launching detached...")
    try:
        p = subprocess.Popen([EXE_PATH], shell=False)
        launch_pid = p.pid
        log(f"Launched PID={launch_pid}")
    except Exception as e:
        raise RuntimeError(f"launch failed: {e}")

    time.sleep(POST_LAUNCH_WAIT)

    t0 = time.time()
    app = None
    main = None
    last_dump = 0
    while time.time() - t0 < ATTACH_TIMEOUT:
        if app is None:
            app, win = try_connect_by_pid(launch_pid)
            if app and win:
                main = win
                log("Attached by PID to top_window()")

        if main is None:
            w = find_window_by_title_keys()
            if w:
                try:
                    app = Application(backend="uia").connect(handle=w.handle)
                    main = app.window(handle=w.handle)
                    log(f"Attached by title to '{main.window_text()}' (pid={w.process_id})")
                except Exception:
                    main = None

        if main:
            try:
                main.wait("visible", timeout=5)
                break
            except Exception:
                main = None

        if time.time() - last_dump > 5:
            log("Waiting for main window... listing top windows:")
            list_top_windows()
            last_dump = time.time()
        time.sleep(0.4)

    if not main:
        raise RuntimeError("attach_or_launch: could not find a running main window")

    try:
        main.maximize()
        time.sleep(POST_MAXIMIZE_WAIT)
    except Exception as e:
        log(f"WARN maximize: {e}")

    time.sleep(POST_ATTACH_SETTLE)
    return app, main

# =========================
# TREE SELECT
# =========================
def _expand(item):
    try:
        item.expand()
    except Exception:
        try:
            item.double_click_input()
        except Exception:
            pass
    time.sleep(0.15)

def _find_tree_root(main):
    try:
        t = main.child
