{"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "definitions": {"AllPublishOptions": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"type": "string"}]}, "AppImageOptions": {"additionalProperties": false, "properties": {"artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template).", "type": ["null", "string"]}, "category": {"description": "The [application category](https://specifications.freedesktop.org/menu-spec/latest/apa.html#main-category-registry).", "type": ["null", "string"]}, "description": {"description": "As [description](./configuration.md#description) from application package.json, but allows you to specify different for Linux.", "type": ["null", "string"]}, "desktop": {"anyOf": [{"$ref": "#/definitions/LinuxDesktopFile"}, {"type": "null"}], "description": "The [Desktop file](https://developer.gnome.org/documentation/guidelines/maintainer/integrating.html#desktop-files)"}, "executableArgs": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "The executable parameters. Pass to executableName"}, "license": {"description": "The path to EULA license file. Defaults to `license.txt` or `eula.txt` (or uppercase variants). Only plain text is supported.", "type": ["null", "string"]}, "mimeTypes": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "The mime types in addition to specified in the file associations. Use it if you don't want to register a new mime type, but reuse existing."}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}]}, "synopsis": {"description": "The [short description](https://www.debian.org/doc/debian-policy/ch-controlfields.html#s-f-Description).", "type": ["null", "string"]}}, "type": "object"}, "AppXOptions": {"additionalProperties": false, "properties": {"addAutoLaunchExtension": {"description": "Whether to add auto launch extension. Defaults to `true` if [electron-winstore-auto-launch](https://github.com/felixrieseberg/electron-winstore-auto-launch) in the dependencies.", "type": "boolean"}, "applicationId": {"description": "The application id. Defaults to `identityName`. This string contains alpha-numeric fields separated by periods. Each field must begin with an ASCII alphabetic character.", "type": "string"}, "artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template).", "type": ["null", "string"]}, "backgroundColor": {"default": "#464646", "description": "The background color of the app tile. See [Visual Elements](https://msdn.microsoft.com/en-us/library/windows/apps/br211471.aspx).", "type": ["null", "string"]}, "customExtensionsPath": {"description": "Relative path to custom extensions xml to be included in an `appmanifest.xml`.", "type": "string"}, "customManifestPath": {"description": "(Advanced Option) Relative path to custom `appmanifest.xml` (file name doesn't matter, it'll be renamed) located in build resources directory.\nSupports the following template macros:\n\n- ${publisher}\n- ${publisherDisplayName}\n- ${version}\n- ${applicationId}\n- ${identityName}\n- ${executable}\n- ${displayName}\n- ${description}\n- ${backgroundColor}\n- ${logo}\n- ${square150x150Logo}\n- ${square44x44Logo}\n- ${lockScreen}\n- ${defaultTile}\n- ${splashScreen}\n- ${arch}\n- ${resourceLanguages}\n- ${extensions}\n- ${minVersion}\n- ${maxVersionTested}", "type": "string"}, "displayName": {"description": "A friendly name that can be displayed to users. Corresponds to [Properties.DisplayName](https://msdn.microsoft.com/en-us/library/windows/apps/br211432.aspx).\nDefaults to the application product name.", "type": ["null", "string"]}, "electronUpdaterAware": {"default": false, "type": "boolean"}, "identityName": {"description": "The name. Corresponds to [Identity.Name](https://msdn.microsoft.com/en-us/library/windows/apps/br211441.aspx). Defaults to the [application name](./configuration.md#metadata).", "type": ["null", "string"]}, "languages": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "The list of [supported languages](https://docs.microsoft.com/en-us/windows/uwp/globalizing/manage-language-and-region#specify-the-supported-languages-in-the-apps-manifest) that will be listed in the Windows Store.\nThe first entry (index 0) will be the default language.\nDefaults to en-US if omitted."}, "makeappxArgs": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}]}, "maxVersionTested": {"default": "arch === Arch.arm64 ? \"10.0.16299.0\" : \"10.0.14316.0\"", "description": "Set the `MaxVersionTested` field in the appx manifest.xml", "type": ["null", "string"]}, "minVersion": {"default": "arch === Arch.arm64 ? \"10.0.16299.0\" : \"10.0.14316.0\"", "description": "Set the MinVersion field in the appx manifest.xml", "type": ["null", "string"]}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}]}, "publisher": {"description": "The Windows Store publisher. Not used if AppX is build for testing. See [AppX Package Code Signing](#appx-package-code-signing) below.", "type": ["null", "string"]}, "publisherDisplayName": {"description": "A friendly name for the publisher that can be displayed to users. Corresponds to [Properties.PublisherDisplayName](https://msdn.microsoft.com/en-us/library/windows/apps/br211460.aspx).\nDefaults to company name from the application metadata.", "type": ["null", "string"]}, "setBuildNumber": {"default": false, "description": "Whether to set build number. See https://github.com/electron-userland/electron-builder/issues/3875", "type": "boolean"}, "showNameOnTiles": {"default": false, "description": "Whether to overlay the app's name on top of tile images on the Start screen. Defaults to `false`. (https://docs.microsoft.com/en-us/uwp/schemas/appxpackage/uapmanifestschema/element-uap-shownameontiles) in the dependencies.", "type": "boolean"}}, "type": "object"}, "ArchType": {"enum": ["arm64", "armv7l", "ia32", "universal", "x64"], "type": "string"}, "AsarOptions": {"additionalProperties": false, "properties": {"ordering": {"type": ["null", "string"]}, "smartUnpack": {"default": true, "description": "Whether to automatically unpack executables files.", "type": "boolean"}}, "type": "object"}, "BitbucketOptions": {"additionalProperties": false, "description": "Bitbucket options.\nhttps://bitbucket.org/\nDefine `BIT<PERSON>UCKET_TOKEN` environment variable.\n\nFor converting an app password to a usable token, you can utilize this\n```typescript\nconvertAppPassword(owner: string, appPassword: string) {\nconst base64encodedData = Buffer.from(`${owner}:${appPassword.trim()}`).toString(\"base64\")\nreturn `Basic ${base64encodedData}`\n}\n```", "properties": {"channel": {"default": "latest", "description": "The channel.", "type": ["null", "string"]}, "owner": {"description": "Repository owner", "type": "string"}, "provider": {"const": "bitbucket", "description": "The provider. Must be `bitbucket`.", "type": "string"}, "publishAutoUpdate": {"default": true, "description": "Whether to publish auto update info files.\n\nAuto update relies only on the first provider in the list (you can specify several publishers).\nThus, probably, there`s no need to upload the metadata files for the other configured providers. But by default will be uploaded.", "type": "boolean"}, "publisherName": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}]}, "requestHeaders": {"$ref": "#/definitions/OutgoingHttpHeaders", "description": "Any custom request headers"}, "slug": {"description": "Repository slug/name", "type": "string"}, "timeout": {"default": 120000, "description": "Request timeout in milliseconds. (Default is 2 minutes; O is ignored)", "type": ["null", "number"]}, "token": {"description": "The [app password](https://bitbucket.org/account/settings/app-passwords) to support auto-update from private bitbucket repositories.", "type": ["null", "string"]}, "updaterCacheDirName": {"type": ["null", "string"]}, "username": {"description": "The user name to support auto-update from private bitbucket repositories.", "type": ["null", "string"]}}, "required": ["owner", "provider", "slug"], "type": "object"}, "CustomNsisBinary": {"additionalProperties": false, "properties": {"checksum": {"default": "VKMiizYdmNdJOWpRGz4trl4lD++BvYP2irAXpMilheUP0pc93iKlWAoP843Vlraj8YG19CVn0j+dCo/hURz9+Q==", "type": ["null", "string"]}, "debugLogging": {"description": "Whether or not to enable NSIS logging for debugging.\nNote: Requires a debug-enabled NSIS build.\nelectron-builder's included `makensis` does not natively support debug-enabled NSIS installers currently, you must supply your own via `customNsisBinary?: CustomNsisBinary`\nIn your custom nsis scripts, you can leverage this functionality via `LogSet` and `LogText`", "type": ["null", "boolean"]}, "url": {"default": "https://github.com/electron-userland/electron-builder-binaries/releases/download", "type": ["null", "string"]}, "version": {"default": "*******", "type": ["null", "string"]}}, "required": ["url"], "type": "object"}, "CustomPublishOptions": {"additionalProperties": {}, "properties": {"provider": {"const": "custom", "description": "The provider. Must be `custom`.", "type": "string"}, "publishAutoUpdate": {"default": true, "description": "Whether to publish auto update info files.\n\nAuto update relies only on the first provider in the list (you can specify several publishers).\nThus, probably, there`s no need to upload the metadata files for the other configured providers. But by default will be uploaded.", "type": "boolean"}, "publisherName": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}]}, "requestHeaders": {"$ref": "#/definitions/OutgoingHttpHeaders", "description": "Any custom request headers"}, "timeout": {"default": 120000, "description": "Request timeout in milliseconds. (Default is 2 minutes; O is ignored)", "type": ["null", "number"]}, "updateProvider": {"description": "The Provider to provide UpdateInfo regarding available updates.  Required\nto use custom providers with electron-updater.", "typeof": "function"}, "updaterCacheDirName": {"type": ["null", "string"]}}, "required": ["provider"], "type": "object"}, "DebOptions": {"additionalProperties": false, "properties": {"afterInstall": {"description": "File path to script to be passed to FPM for `--after-install` arg.", "type": ["null", "string"]}, "afterRemove": {"description": "File path to script to be passed to FPM for `--after-remove` arg.", "type": ["null", "string"]}, "appArmorProfile": {"description": "File path to custom AppArmor profile (Ubuntu 24+)", "type": ["null", "string"]}, "artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template).", "type": ["null", "string"]}, "category": {"description": "The [application category](https://specifications.freedesktop.org/menu-spec/latest/apa.html#main-category-registry).", "type": ["null", "string"]}, "compression": {"anyOf": [{"enum": ["bzip2", "gz", "lzo", "xz"], "type": "string"}, {"type": "null"}], "default": "xz", "description": "The compression type."}, "depends": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": ["libgtk-3-0", "libnotify4", "libnss3", "libxss1", "libxtst6", "xdg-utils", "libatspi2.0-0", "libuuid1", "libsecret-1-0"], "description": "Package dependencies.\nIf need to support Debian, `libappindicator1` should be removed, it is [deprecated in Debian](https://bugs.debian.org/cgi-bin/bugreport.cgi?bug=895037).\nIf need to support KDE, `gconf2` and `gconf-service` should be removed as it's no longer used [by GNOME](https://packages.debian.org/bullseye/gconf2)."}, "description": {"description": "As [description](./configuration.md#description) from application package.json, but allows you to specify different for Linux.", "type": ["null", "string"]}, "desktop": {"anyOf": [{"$ref": "#/definitions/LinuxDesktopFile"}, {"type": "null"}], "description": "The [Desktop file](https://developer.gnome.org/documentation/guidelines/maintainer/integrating.html#desktop-files)"}, "executableArgs": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "The executable parameters. Pass to executableName"}, "fpm": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "*Advanced only* The [fpm](https://fpm.readthedocs.io/en/latest/cli-reference.html) options.\n\nExample: `[\"--before-install=build/deb-preinstall.sh\", \"--after-upgrade=build/deb-postinstall.sh\"]`"}, "icon": {"type": "string"}, "maintainer": {"type": ["null", "string"]}, "mimeTypes": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "The mime types in addition to specified in the file associations. Use it if you don't want to register a new mime type, but reuse existing."}, "packageCategory": {"description": "The [package category](https://www.debian.org/doc/debian-policy/ch-controlfields.html#s-f-Section).", "type": ["null", "string"]}, "packageName": {"description": "The name of the package.", "type": ["null", "string"]}, "priority": {"description": "The [Priority](https://www.debian.org/doc/debian-policy/ch-controlfields.html#s-f-Priority) attribute.", "type": ["null", "string"]}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}]}, "recommends": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": ["libappindicator3-1"], "description": "The [recommended package dependencies](https://www.debian.org/doc/debian-policy/ch-relationships.html#s-binarydeps)."}, "synopsis": {"description": "The [short description](https://www.debian.org/doc/debian-policy/ch-controlfields.html#s-f-Description).", "type": ["null", "string"]}, "vendor": {"type": ["null", "string"]}}, "type": "object"}, "DmgContent": {"additionalProperties": false, "properties": {"name": {"description": "The name of the file within the DMG. Defaults to basename of `path`.", "type": "string"}, "path": {"description": "The path of the file within the DMG.", "type": "string"}, "type": {"enum": ["dir", "file", "link"], "type": "string"}, "x": {"description": "The device-independent pixel offset from the left of the window to the **center** of the icon.", "type": "number"}, "y": {"description": "The device-independent pixel offset from the top of the window to the **center** of the icon.", "type": "number"}}, "required": ["x", "y"], "type": "object"}, "DmgOptions": {"additionalProperties": false, "properties": {"artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template).", "type": ["null", "string"]}, "background": {"description": "The path to background image (default: `build/background.tiff` or `build/background.png` if exists). The resolution of this file determines the resolution of the installer window.\nIf background is not specified, use `window.size`. Default locations expected background size to be 540x380.", "type": ["null", "string"]}, "backgroundColor": {"description": "The background color (accepts css colors). Defaults to `#ffffff` (white) if no background image.", "type": ["null", "string"]}, "contents": {"description": "The content — to customize icon locations. The x and y coordinates refer to the position of the **center** of the icon (at 1x scale), and do not take the label into account.", "items": {"$ref": "#/definitions/DmgContent"}, "type": "array"}, "format": {"default": "UDZO", "description": "The disk image format. `ULFO` (lzfse-compressed image (OS X 10.11+ only)).", "enum": ["UDBZ", "UDCO", "UDRO", "UDRW", "UDZO", "ULFO"], "type": "string"}, "icon": {"description": "The path to DMG icon (volume icon), which will be shown when mounted, relative to the [build resources](./contents.md#extraresources) or to the project directory.\nDefaults to the application icon (`build/icon.icns`).", "type": ["null", "string"]}, "iconSize": {"default": 80, "description": "The size of all the icons inside the DMG.", "type": ["null", "number"]}, "iconTextSize": {"default": 12, "description": "The size of all the icon texts inside the DMG.", "type": ["null", "number"]}, "internetEnabled": {"default": false, "description": "Whether to create internet-enabled disk image (when it is downloaded using a browser it will automatically decompress the image, put the application on the desktop, unmount and remove the disk image file).", "type": "boolean"}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}]}, "sign": {"default": false, "description": "Whether to sign the DMG or not. Signing is not required and will lead to unwanted errors in combination with notarization requirements.", "type": "boolean"}, "title": {"default": "${productName} ${version}", "description": "The title of the produced DMG, which will be shown when mounted (volume name).\n\nMacro `${productName}`, `${version}` and `${name}` are supported.", "type": ["null", "string"]}, "window": {"$ref": "#/definitions/DmgWindow", "description": "The DMG window position and size. With y co-ordinates running from bottom to top.\n\nThe Finder makes sure that the window will be on the user’s display, so if you want your window at the top left of the display you could use `\"x\": 0, \"y\": 100000` as the x, y co-ordinates.\nIt is not to be possible to position the window relative to the [top left](https://github.com/electron-userland/electron-builder/issues/3990#issuecomment-512960957) or relative to the center of the user’s screen."}, "writeUpdateInfo": {"default": true, "type": "boolean"}}, "type": "object"}, "DmgWindow": {"additionalProperties": false, "properties": {"height": {"description": "The height. Defaults to background image height or 380.", "type": "number"}, "width": {"description": "The width. Defaults to background image width or 540.", "type": "number"}, "x": {"default": 400, "description": "The X position relative to left of the screen.", "type": "number"}, "y": {"default": 100, "description": "The Y position relative to bottom of the screen.", "type": "number"}}, "type": "object"}, "ElectronBrandingOptions": {"additionalProperties": false, "description": "Electron distributables branding options.", "properties": {"productName": {"type": "string"}, "projectName": {"type": "string"}}, "type": "object"}, "ElectronDownloadOptions": {"additionalProperties": false, "properties": {"arch": {"type": "string"}, "cache": {"description": "The [cache location](https://github.com/electron-userland/electron-download#cache-location).", "type": ["null", "string"]}, "customDir": {"type": ["null", "string"]}, "customFilename": {"type": ["null", "string"]}, "isVerifyChecksum": {"type": "boolean"}, "mirror": {"description": "The mirror.", "type": ["null", "string"]}, "platform": {"enum": ["darwin", "linux", "mas", "win32"], "type": "string"}, "strictSSL": {"type": "boolean"}, "version": {"type": "string"}}, "type": "object"}, "FileAssociation": {"additionalProperties": false, "description": "File associations.\n\nmacOS (corresponds to [CFBundleDocumentTypes](https://developer.apple.com/library/content/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html#//apple_ref/doc/uid/********-101685)), NSIS, and MSI only.\n\nOn Windows (NSIS) works only if [nsis.perMachine](https://www.electron.build/nsis) is set to `true`.", "properties": {"description": {"description": "*windows-only.* The description.", "type": ["null", "string"]}, "ext": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "The extension (minus the leading period). e.g. `png`."}, "icon": {"description": "The path to icon (`.icns` for MacOS and `.ico` for Windows), relative to `build` (build resources directory). Defaults to `${firstExt}.icns`/`${firstExt}.ico` (if several extensions specified, first is used) or to application icon.\n\nNot supported on Linux, file issue if need (default icon will be `x-office-document`). Not supported on MSI.", "type": ["null", "string"]}, "isPackage": {"description": "*macOS-only* Whether the document is distributed as a bundle. If set to true, the bundle directory is treated as a file. Corresponds to `LSTypeIsPackage`.", "type": "boolean"}, "mimeType": {"description": "*linux-only.* The mime-type.", "type": ["null", "string"]}, "name": {"description": "The name. e.g. `PNG`. Defaults to `ext`.", "type": ["null", "string"]}, "rank": {"default": "<PERSON><PERSON><PERSON>", "description": "*macOS-only* The app’s rank with respect to the type. The value can be `Owner`, `Default`, `Alternate`, or `None`. Corresponds to `LSHandlerRank`.", "type": "string"}, "role": {"default": "Editor", "description": "*macOS-only* The app’s role with respect to the type. The value can be `Editor`, `Viewer`, `Shell`, or `None`. Corresponds to `CFBundleTypeRole`.", "type": "string"}}, "required": ["ext"], "type": "object"}, "FileSet": {"additionalProperties": false, "properties": {"filter": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "The [glob patterns](./file-patterns.md). Defaults to \"**\\/*\""}, "from": {"description": "The source path relative to and defaults to:\n\n - the [app directory](configuration.md#directories) for `files`,\n - the project directory for `extraResources` and `extraFiles`.\nIf you don't use two-package.json structure and don't set custom app directory, app directory equals to project directory.", "type": "string"}, "to": {"description": "The destination path relative to and defaults to:\n\n - the asar archive root for `files`,\n - the app's content directory for `extraFiles`,\n - the app's resource directory for `extraResources`.", "type": "string"}}, "type": "object"}, "FlatpakOptions": {"additionalProperties": false, "properties": {"artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template).", "type": ["null", "string"]}, "base": {"description": "Start with the files from the specified application. This can be used to create applications that extend another application.\nDefaults to [org.electronjs.Electron2.BaseApp](https://github.com/flathub/org.electronjs.Electron2.BaseApp).\n\nSee [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).", "type": "string"}, "baseVersion": {"description": "Use this specific version of the application specified in base. Defaults to `20.08`.\n\nSee [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).", "type": "string"}, "branch": {"description": "The branch to use when exporting the application. Defaults to `master`.\n\nSee [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).", "type": "string"}, "category": {"description": "The [application category](https://specifications.freedesktop.org/menu-spec/latest/apa.html#main-category-registry).", "type": ["null", "string"]}, "description": {"description": "As [description](./configuration.md#description) from application package.json, but allows you to specify different for Linux.", "type": ["null", "string"]}, "desktop": {"anyOf": [{"$ref": "#/definitions/LinuxDesktopFile"}, {"type": "null"}], "description": "The [Desktop file](https://developer.gnome.org/documentation/guidelines/maintainer/integrating.html#desktop-files)"}, "executableArgs": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "The executable parameters. Pass to executableName"}, "files": {"description": "Files to copy directly into the app. Should be a list of [source, dest] tuples. Source should be a relative/absolute path to a file/directory to copy into the flatpak, and dest should be the path inside the app install prefix (e.g. /share/applications/).\n\nSee [@malept/flatpak-bundler documentation](https://github.com/malept/flatpak-bundler#build-options).", "items": {"items": [{"type": "string"}, {"type": "string"}], "maxItems": 2, "minItems": 2, "type": "array"}, "type": "array"}, "finishArgs": {"description": "An array of arguments passed to the flatpak build-finish command. Defaults to:\n```json\n[\n  // Wayland/X11 Rendering\n  \"--socket=wayland\",\n  \"--socket=x11\",\n  \"--share=ipc\",\n  // Open GL\n  \"--device=dri\",\n  // Audio output\n  \"--socket=pulseaudio\",\n  // Read/write home directory access\n  \"--filesystem=home\",\n  // Allow communication with network\n  \"--share=network\",\n  // System notifications with libnotify\n  \"--talk-name=org.freedesktop.Notifications\",\n]\n```\n\nSee [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).", "items": {"type": "string"}, "type": "array"}, "license": {"description": "The path to EULA license file. Defaults to `license.txt` or `eula.txt` (or uppercase variants). Only plain text is supported.", "type": ["null", "string"]}, "mimeTypes": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "The mime types in addition to specified in the file associations. Use it if you don't want to register a new mime type, but reuse existing."}, "modules": {"description": "An array of objects specifying the modules to be built in order.\n\nSee [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).", "items": {}, "type": "array"}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}]}, "runtime": {"description": "The name of the runtime that the application uses. Defaults to `org.freedesktop.Platform`.\n\nSee [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).", "type": "string"}, "runtimeVersion": {"description": "The version of the runtime that the application uses. Defaults to `20.08`.\n\nSee [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).", "type": "string"}, "sdk": {"description": "The name of the development runtime that the application builds with. Defaults to `org.freedesktop.Sdk`.\n\nSee [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).", "type": "string"}, "symlinks": {"description": "Symlinks to create in the app files. Should be a list of [target, location] symlink tuples. Target can be either a relative or absolute path inside the app install prefix, and location should be a absolute path inside the prefix to create the symlink at.\n\nSee [@malept/flatpak-bundler documentation](https://github.com/malept/flatpak-bundler#build-options).", "items": {"items": [{"type": "string"}, {"type": "string"}], "maxItems": 2, "minItems": 2, "type": "array"}, "type": "array"}, "synopsis": {"description": "The [short description](https://www.debian.org/doc/debian-policy/ch-controlfields.html#s-f-Description).", "type": ["null", "string"]}, "useWaylandFlags": {"description": "Whether to enable the Wayland specific flags (`--enable-features=UseOzonePlatform --ozone-platform=wayland`) in the wrapper script. These flags are only available starting with Electron version 12. Defaults to `false`.", "type": "boolean"}}, "type": "object"}, "FuseOptionsV1": {"additionalProperties": false, "description": "All options come from [@electron/fuses](https://github.com/electron/fuses)\nRef: https://raw.githubusercontent.com/electron/electron/refs/heads/main/docs/tutorial/fuses.md", "properties": {"enableCookieEncryption": {"description": "The cookieEncryption fuse toggles whether the cookie store on disk is encrypted using OS level cryptography keys.  By default the sqlite database that Chromium uses to store cookies stores the values in plaintext.  If you wish to ensure your apps cookies are encrypted in the same way Chrome does then you should enable this fuse.  Please note it is a one-way transition, if you enable this fuse existing unencrypted cookies will be encrypted-on-write but if you then disable the fuse again your cookie store will effectively be corrupt and useless.  Most apps can safely enable this fuse.", "type": "boolean"}, "enableEmbeddedAsarIntegrityValidation": {"description": "The embeddedAsarIntegrityValidation fuse toggles an experimental feature on macOS that validates the content of the `app.asar` file when it is loaded.  This feature is designed to have a minimal performance impact but may marginally slow down file reads from inside the `app.asar` archive.\nCurrently, ASAR integrity checking is supported on:\n\n - macOS as of electron>=16.0.0\n - Windows as of electron>=30.0.0\n\nFor more information on how to use asar integrity validation please read the [Asar Integrity](https://github.com/electron/electron/blob/main/docs/tutorial/asar-integrity.md) documentation.", "type": "boolean"}, "enableNodeCliInspectArguments": {"description": "The nodeCliInspect fuse toggles whether the `--inspect`, `--inspect-brk`, etc. flags are respected or not.  When disabled it also ensures that `SIGUSR1` signal does not initialize the main process inspector.  Most apps can safely disable this fuse.", "type": "boolean"}, "enableNodeOptionsEnvironmentVariable": {"description": "The nodeOptions fuse toggles whether the [`NODE_OPTIONS`](https://nodejs.org/api/cli.html#node_optionsoptions)  and [`NODE_EXTRA_CA_CERTS`](https://github.com/nodejs/node/blob/main/doc/api/cli.md#node_extra_ca_certsfile) environment variables are respected.  The `NODE_OPTIONS` environment variable can be used to pass all kinds of custom options to the Node.js runtime and isn't typically used by apps in production.  Most apps can safely disable this fuse.", "type": "boolean"}, "grantFileProtocolExtraPrivileges": {"description": "The grantFileProtocolExtraPrivileges fuse changes whether pages loaded from the `file://` protocol are given privileges beyond what they would receive in a traditional web browser.  This behavior was core to Electron apps in original versions of Electron but is no longer required as apps should be [serving local files from custom protocols](https://github.com/electron/electron/blob/main/docs/tutorial/security.md#18-avoid-usage-of-the-file-protocol-and-prefer-usage-of-custom-protocols) now instead.  If you aren't serving pages from `file://` you should disable this fuse.\nThe extra privileges granted to the `file://` protocol by this fuse are incompletely documented below:\n\n - `file://` protocol pages can use `fetch` to load other assets over `file://`\n - `file://` protocol pages can use service workers\n - `file://` protocol pages have universal access granted to child frames also running on `file://` protocols regardless of sandbox settings", "type": "boolean"}, "loadBrowserProcessSpecificV8Snapshot": {"description": "The loadBrowserProcessSpecificV8Snapshot fuse changes which V8 snapshot file is used for the browser process.  By default Electron's processes will all use the same V8 snapshot file.  When this fuse is enabled the browser process uses the file called `browser_v8_context_snapshot.bin` for its V8 snapshot. The other processes will use the V8 snapshot file that they normally do.", "type": "boolean"}, "onlyLoadAppFromAsar": {"description": "The onlyLoadAppFromAsar fuse changes the search system that Electron uses to locate your app code.  By default Electron will search in the following order `app.asar` -> `app` -> `default_app.asar`.  When this fuse is enabled the search order becomes a single entry `app.asar` thus ensuring that when combined with the `embeddedAsarIntegrityValidation` fuse it is impossible to load non-validated code.", "type": "boolean"}, "resetAdHocDarwinSignature": {"description": "Resets the app signature, specifically used for macOS.\nNote: This should be unneeded since electron-builder signs the app directly after flipping the fuses.\nRef: https://github.com/electron/fuses?tab=readme-ov-file#apple-silicon", "type": "boolean"}, "runAsNode": {"description": "The runAsNode fuse toggles whether the `ELECTRON_RUN_AS_NODE` environment variable is respected or not.  Please note that if this fuse is disabled then `process.fork` in the main process will not function as expected as it depends on this environment variable to function. Instead, we recommend that you use [Utility Processes](https://github.com/electron/electron/blob/main/docs/api/utility-process.md), which work for many use cases where you need a standalone Node.js process (like a Sqlite server process or similar scenarios).", "type": "boolean"}}, "type": "object"}, "GenericServerOptions": {"additionalProperties": false, "description": "Generic (any HTTP(S) server) options.\nIn all publish options [File Macros](./file-patterns.md#file-macros) are supported.", "properties": {"channel": {"default": "latest", "description": "The channel.", "type": ["null", "string"]}, "provider": {"const": "generic", "description": "The provider. Must be `generic`.", "type": "string"}, "publishAutoUpdate": {"default": true, "description": "Whether to publish auto update info files.\n\nAuto update relies only on the first provider in the list (you can specify several publishers).\nThus, probably, there`s no need to upload the metadata files for the other configured providers. But by default will be uploaded.", "type": "boolean"}, "publisherName": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}]}, "requestHeaders": {"$ref": "#/definitions/OutgoingHttpHeaders", "description": "Any custom request headers"}, "timeout": {"default": 120000, "description": "Request timeout in milliseconds. (Default is 2 minutes; O is ignored)", "type": ["null", "number"]}, "updaterCacheDirName": {"type": ["null", "string"]}, "url": {"description": "The base url. e.g. `https://bucket_name.s3.amazonaws.com`.", "type": "string"}, "useMultipleRangeRequest": {"description": "Whether to use multiple range requests for differential update. Defaults to `true` if `url` doesn't contain `s3.amazonaws.com`.", "type": "boolean"}}, "required": ["provider", "url"], "type": "object"}, "GithubOptions": {"additionalProperties": false, "description": "[GitHub](https://help.github.com/articles/about-releases/) options.\n\nGitHub [personal access token](https://help.github.com/articles/creating-an-access-token-for-command-line-use/) is required. You can generate by going to [https://github.com/settings/tokens/new](https://github.com/settings/tokens/new). The access token should have the repo scope/permission.\nDefine `GH_TOKEN` environment variable.", "properties": {"channel": {"default": "latest", "description": "The channel.", "type": ["null", "string"]}, "host": {"default": "github.com", "description": "The host (including the port if need).", "type": ["null", "string"]}, "owner": {"description": "The owner.", "type": ["null", "string"]}, "private": {"description": "Whether to use private github auto-update provider if `GH_TOKEN` environment variable is defined. See [Private GitHub Update Repo](./auto-update.md#private-github-update-repo).", "type": ["null", "boolean"]}, "protocol": {"anyOf": [{"enum": ["http", "https"], "type": "string"}, {"type": "null"}], "default": "https", "description": "The protocol. GitHub Publisher supports only `https`."}, "provider": {"const": "github", "description": "The provider. Must be `github`.", "type": "string"}, "publishAutoUpdate": {"default": true, "description": "Whether to publish auto update info files.\n\nAuto update relies only on the first provider in the list (you can specify several publishers).\nThus, probably, there`s no need to upload the metadata files for the other configured providers. But by default will be uploaded.", "type": "boolean"}, "publisherName": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}]}, "releaseType": {"anyOf": [{"enum": ["draft", "prerelease", "release"], "type": "string"}, {"type": "null"}], "default": "draft", "description": "The type of release. By default `draft` release will be created.\n\nAlso you can set release type using environment variable. If `EP_DRAFT`is set to `true` — `draft`, if `EP_PRE_RELEASE`is set to `true` — `prerelease`."}, "repo": {"description": "The repository name. [Detected automatically](#github-repository-and-bintray-package).", "type": ["null", "string"]}, "requestHeaders": {"$ref": "#/definitions/OutgoingHttpHeaders", "description": "Any custom request headers"}, "timeout": {"default": 120000, "description": "Request timeout in milliseconds. (Default is 2 minutes; O is ignored)", "type": ["null", "number"]}, "token": {"description": "The access token to support auto-update from private github repositories. Never specify it in the configuration files. Only for [setFeedURL](./auto-update.md#appupdatersetfeedurloptions).", "type": ["null", "string"]}, "updaterCacheDirName": {"type": ["null", "string"]}, "vPrefixedTagName": {"default": true, "description": "Whether to use `v`-prefixed tag name.", "type": "boolean"}}, "required": ["provider"], "type": "object"}, "KeygenOptions": {"additionalProperties": false, "description": "Keygen options.\nhttps://keygen.sh/\nDefine `KEYGEN_TOKEN` environment variable.", "properties": {"account": {"description": "Keygen account's UUID", "type": "string"}, "channel": {"anyOf": [{"enum": ["alpha", "beta", "dev", "rc", "stable"], "type": "string"}, {"type": "null"}], "default": "stable", "description": "The channel."}, "host": {"default": "api.keygen.sh", "description": "Keygen host for self-hosted instances", "type": "string"}, "platform": {"description": "The target Platform. Is set programmatically explicitly during publishing.", "type": ["null", "string"]}, "product": {"description": "Keygen product's UUID", "type": "string"}, "provider": {"const": "keygen", "description": "The provider. Must be `keygen`.", "type": "string"}, "publishAutoUpdate": {"default": true, "description": "Whether to publish auto update info files.\n\nAuto update relies only on the first provider in the list (you can specify several publishers).\nThus, probably, there`s no need to upload the metadata files for the other configured providers. But by default will be uploaded.", "type": "boolean"}, "publisherName": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}]}, "requestHeaders": {"$ref": "#/definitions/OutgoingHttpHeaders", "description": "Any custom request headers"}, "timeout": {"default": 120000, "description": "Request timeout in milliseconds. (Default is 2 minutes; O is ignored)", "type": ["null", "number"]}, "updaterCacheDirName": {"type": ["null", "string"]}}, "required": ["account", "product", "provider"], "type": "object"}, "LinuxConfiguration": {"additionalProperties": false, "properties": {"appId": {"default": "com.electron.${name}", "description": "The application id. Used as [CFBundleIdentifier](https://developer.apple.com/library/ios/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html#//apple_ref/doc/uid/********-102070) for MacOS and as\n[Application User Model ID](https://msdn.microsoft.com/en-us/library/windows/desktop/dd378459(v=vs.85).aspx) for Windows (NSIS target only, Squirrel.Windows not supported). It is strongly recommended that an explicit ID is set.", "type": ["null", "string"]}, "artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template). Defaults to `${productName}-${version}.${ext}` (some target can have other defaults, see corresponding options).", "type": ["null", "string"]}, "asar": {"anyOf": [{"$ref": "#/definitions/AsarOptions"}, {"type": ["null", "boolean"]}], "default": true, "description": "Whether to package the application's source code into an archive, using [Electron's archive format](http://electron.atom.io/docs/tutorial/application-packaging/).\n\nNode modules, that must be unpacked, will be detected automatically, you don't need to explicitly set [asarUnpack](#asarUnpack) - please file an issue if this doesn't work."}, "asarUnpack": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "A [glob patterns](./file-patterns.md) relative to the [app directory](#directories), which specifies which files to unpack when creating the [asar](http://electron.atom.io/docs/tutorial/application-packaging/) archive."}, "category": {"description": "The [application category](https://specifications.freedesktop.org/menu-spec/latest/apa.html#main-category-registry).", "type": ["null", "string"]}, "compression": {"anyOf": [{"enum": ["maximum", "normal", "store"], "type": "string"}, {"type": "null"}], "default": "normal", "description": "The compression level. If you want to rapidly test build, `store` can reduce build time significantly. `maximum` doesn't lead to noticeable size difference, but increase build time."}, "cscKeyPassword": {"type": ["null", "string"]}, "cscLink": {"type": ["null", "string"]}, "defaultArch": {"type": "string"}, "description": {"description": "As [description](./configuration.md#description) from application package.json, but allows you to specify different for Linux.", "type": ["null", "string"]}, "desktop": {"anyOf": [{"$ref": "#/definitions/LinuxDesktopFile"}, {"type": "null"}], "description": "The [Desktop file](https://developer.gnome.org/documentation/guidelines/maintainer/integrating.html#desktop-files)"}, "detectUpdateChannel": {"default": true, "description": "Whether to infer update channel from application version pre-release components. e.g. if version `0.12.1-alpha.1`, channel will be set to `alpha`. Otherwise to `latest`.\nThis does *not* apply to github publishing, which will [never auto-detect the update channel](https://github.com/electron-userland/electron-builder/issues/8589).", "type": "boolean"}, "disableDefaultIgnoredFiles": {"default": false, "description": "Whether to exclude all default ignored files(https://www.electron.build/contents#files) and options. Defaults to `false`.", "type": ["null", "boolean"]}, "electronLanguages": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "The electron locales to keep. By default, all Electron locales used as-is."}, "electronUpdaterCompatibility": {"description": "The [electron-updater compatibility](./auto-update.md#compatibility) semver range.", "type": ["null", "string"]}, "executableArgs": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "The executable parameters. Pass to executableName"}, "executableName": {"description": "The executable name. Defaults to `productName`.", "type": ["null", "string"]}, "extraFiles": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"items": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "description": "The same as [extraResources](#extraresources) but copy into the app's content directory (`Contents` for MacOS, root directory for Linux and Windows)."}, "extraResources": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"items": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "description": "A [glob patterns](./file-patterns.md) relative to the project directory, when specified, copy the file or directory with matching names directly into the app's resources directory (`Contents/Resources` for MacOS, `resources` for Linux and Windows).\n\nFile patterns (and support for `from` and `to` fields) the same as for [files](#files)."}, "fileAssociations": {"anyOf": [{"$ref": "#/definitions/FileAssociation"}, {"items": {"$ref": "#/definitions/FileAssociation"}, "type": "array"}], "description": "The file associations."}, "files": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"items": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "description": "A [glob patterns](./file-patterns.md) relative to the [app directory](configuration.md#directories), which specifies which files to include when copying files to create the package.\n\nDefaults to:\n```json\n[\n\"**\\/*\",\n\"!**\\/node_modules/*\\/{CHANGELOG.md,README.md,README,readme.md,readme}\",\n\"!**\\/node_modules/*\\/{test,__tests__,tests,powered-test,example,examples}\",\n\"!**\\/node_modules/*.d.ts\",\n\"!**\\/node_modules/.bin\",\n\"!**\\/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}\",\n\"!.editorconfig\",\n\"!**\\/._*\",\n\"!**\\/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}\",\n\"!**\\/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}\",\n\"!**\\/{appveyor.yml,.travis.yml,circle.yml}\",\n\"!**\\/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}\"\n]\n```\n\nDevelopment dependencies are never copied in any case. You don't need to ignore it explicitly. Hidden files are not ignored by default, but all files that should be ignored, are ignored by default.\n\nDefault pattern \\`**\\/*\\` **is not added to your custom** if some of your patterns is not ignore (i.e. not starts with `!`). `package.json` and \\`**\\/node_modules/**\\/*` (only production dependencies will be copied) is added to your custom in any case. All default ignores are added in any case — you don't need to repeat it if you configure own patterns.\n\nMay be specified in the platform options (e.g. in the [mac](mac.md)).\n\nYou may also specify custom source and destination directories by using `FileSet` objects instead of simple glob patterns.\n\n```json\n[\n{\n\"from\": \"path/to/source\",\n\"to\": \"path/to/destination\",\n\"filter\": [\"**\\/*\", \"!foo/*.js\"]\n}\n]\n```\n\nYou can use [file macros](./file-patterns.md#file-macros) in the `from` and `to` fields as well. `from` and `to` can be files and you can use this to [rename](https://github.com/electron-userland/electron-builder/issues/1119) a file while packaging."}, "forceCodeSigning": {"description": "Whether to fail if app will be not code signed.", "type": "boolean"}, "generateUpdatesFilesForAllChannels": {"default": false, "description": "Please see [Building and Releasing using Channels](https://github.com/electron-userland/electron-builder/issues/1182#issuecomment-324947139).", "type": "boolean"}, "icon": {"description": "The path to icon set directory or one png file, relative to the [build resources](./contents.md#extraresources) or to the project directory. The icon filename must contain the size (e.g. 32x32.png) of the icon.\nBy default will be generated automatically based on the macOS icns file.", "type": "string"}, "maintainer": {"description": "The maintainer. Defaults to [author](./configuration.md#author).", "type": ["null", "string"]}, "mimeTypes": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "The mime types in addition to specified in the file associations. Use it if you don't want to register a new mime type, but reuse existing."}, "packageCategory": {"description": "backward compatibility + to allow specify fpm-only category for all possible fpm targets in one place", "type": ["null", "string"]}, "protocols": {"anyOf": [{"$ref": "#/definitions/Protocol"}, {"items": {"$ref": "#/definitions/Protocol"}, "type": "array"}], "description": "The URL protocol schemes."}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}], "description": "Publisher configuration. See [Auto Update](./publish.md) for more information."}, "releaseInfo": {"$ref": "#/definitions/ReleaseInfo", "description": "The release info. Intended for command line usage:\n\n```\n-c.releaseInfo.releaseNotes=\"new features\"\n```"}, "synopsis": {"description": "The [short description](https://www.debian.org/doc/debian-policy/ch-controlfields.html#s-f-Description).", "type": ["null", "string"]}, "target": {"anyOf": [{"$ref": "#/definitions/TargetConfiguration"}, {"items": {"anyOf": [{"$ref": "#/definitions/TargetConfiguration"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "default": "AppImage", "description": "Target package type: list of `AppImage`, `flatpak`, `snap`, `deb`, `rpm`, `freebsd`, `pacman`, `p5p`, `apk`, `7z`, `zip`, `tar.xz`, `tar.lz`, `tar.gz`, `tar.bz2`, `dir`.\n\nelectron-builder [docker image](./multi-platform-build.md#docker) can be used to build Linux targets on any platform.\n\nPlease [do not put an AppImage into another archive](https://github.com/probonopd/AppImageKit/wiki/Creating-AppImages#common-mistake) like a .zip or .tar.gz."}, "vendor": {"description": "The vendor. Defaults to [author](./configuration.md#author).", "type": ["null", "string"]}}, "type": "object"}, "LinuxDesktopFile": {"additionalProperties": false, "description": "Example Spec: https://specifications.freedesktop.org/desktop-entry-spec/latest/example.html", "properties": {"desktopActions": {"anyOf": [{"typeof": "function"}, {"type": "null"}], "description": "`[Desktop Actions <ActionName>]` metadata entries (name to value).\n\nConfig Example:\n```js\ndesktopActions: {\n   NewWindow: {\n      Name: 'New Window',\n      Exec: 'app --new-window',\n   }\n}\n```"}, "entry": {"anyOf": [{"typeof": "function"}, {"type": "null"}], "description": "`[Desktop Entry]` metadata entries (name to value). Overwrites default values calculated by electron-builder"}}, "type": "object"}, "LinuxTargetSpecificOptions": {"additionalProperties": false, "properties": {"afterInstall": {"description": "File path to script to be passed to FPM for `--after-install` arg.", "type": ["null", "string"]}, "afterRemove": {"description": "File path to script to be passed to FPM for `--after-remove` arg.", "type": ["null", "string"]}, "appArmorProfile": {"description": "File path to custom AppArmor profile (Ubuntu 24+)", "type": ["null", "string"]}, "artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template).", "type": ["null", "string"]}, "category": {"description": "The [application category](https://specifications.freedesktop.org/menu-spec/latest/apa.html#main-category-registry).", "type": ["null", "string"]}, "compression": {"anyOf": [{"enum": ["bzip2", "gz", "lzo", "xz"], "type": "string"}, {"type": "null"}], "default": "xz", "description": "The compression type."}, "depends": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Package dependencies.\n`rpm` defaults to `[\"gtk3\", \"libnotify\", \"nss\", \"libXScrnSaver\", \"(libXtst or libXtst6)\", \"xdg-utils\", \"at-spi2-core\", \"(libuuid or libuuid1)\"]`\n`pacman` defaults to `[\"c-ares\", \"ffmpeg\", \"gtk3\", \"http-parser\", \"libevent\", \"libvpx\", \"libxslt\", \"libxss\", \"minizip\", \"nss\", \"re2\", \"snappy\", \"libnotify\", \"libappindicator-gtk3\"]`"}, "description": {"description": "As [description](./configuration.md#description) from application package.json, but allows you to specify different for Linux.", "type": ["null", "string"]}, "desktop": {"anyOf": [{"$ref": "#/definitions/LinuxDesktopFile"}, {"type": "null"}], "description": "The [Desktop file](https://developer.gnome.org/documentation/guidelines/maintainer/integrating.html#desktop-files)"}, "executableArgs": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "The executable parameters. Pass to executableName"}, "fpm": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "*Advanced only* The [fpm](https://fpm.readthedocs.io/en/latest/cli-reference.html) options.\n\nExample: `[\"--before-install=build/deb-preinstall.sh\", \"--after-upgrade=build/deb-postinstall.sh\"]`"}, "icon": {"type": "string"}, "maintainer": {"type": ["null", "string"]}, "mimeTypes": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "The mime types in addition to specified in the file associations. Use it if you don't want to register a new mime type, but reuse existing."}, "packageCategory": {"description": "The package category.", "type": ["null", "string"]}, "packageName": {"description": "The name of the package.", "type": ["null", "string"]}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}]}, "synopsis": {"description": "The [short description](https://www.debian.org/doc/debian-policy/ch-controlfields.html#s-f-Description).", "type": ["null", "string"]}, "vendor": {"type": ["null", "string"]}}, "type": "object"}, "MacConfiguration": {"additionalProperties": false, "properties": {"additionalArguments": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Array of strings specifying additional arguments to pass to the `codesign` command used to sign a specific file.\n\nSome subresources that you may include in your Electron app may need to be signed with --deep, this is not typically safe to apply to the entire Electron app and therefore should be applied to just your file.\nUsage Example: `['--deep']`"}, "appId": {"default": "com.electron.${name}", "description": "The application id. Used as [CFBundleIdentifier](https://developer.apple.com/library/ios/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html#//apple_ref/doc/uid/********-102070) for MacOS and as\n[Application User Model ID](https://msdn.microsoft.com/en-us/library/windows/desktop/dd378459(v=vs.85).aspx) for Windows (NSIS target only, Squirrel.Windows not supported). It is strongly recommended that an explicit ID is set.", "type": ["null", "string"]}, "artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template). Defaults to `${productName}-${version}.${ext}` (some target can have other defaults, see corresponding options).", "type": ["null", "string"]}, "asar": {"anyOf": [{"$ref": "#/definitions/AsarOptions"}, {"type": ["null", "boolean"]}], "default": true, "description": "Whether to package the application's source code into an archive, using [Electron's archive format](http://electron.atom.io/docs/tutorial/application-packaging/).\n\nNode modules, that must be unpacked, will be detected automatically, you don't need to explicitly set [asarUnpack](#asarUnpack) - please file an issue if this doesn't work."}, "asarUnpack": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "A [glob patterns](./file-patterns.md) relative to the [app directory](#directories), which specifies which files to unpack when creating the [asar](http://electron.atom.io/docs/tutorial/application-packaging/) archive."}, "binaries": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Paths of any extra binaries that need to be signed."}, "bundleShortVersion": {"description": "The `CFBundleShortVersionString`. Do not use it unless you need to.", "type": ["null", "string"]}, "bundleVersion": {"description": "The `CFBundleVersion`. Do not use it unless [you need to](https://github.com/electron-userland/electron-builder/issues/565#issuecomment-230678643).", "type": ["null", "string"]}, "category": {"description": "The application category type, as shown in the Finder via *View -> Arrange by Application Category* when viewing the Applications directory.\n\nFor example, `\"category\": \"public.app-category.developer-tools\"` will set the application category to *Developer Tools*.\n\nValid values are listed in [Apple's documentation](https://developer.apple.com/library/ios/documentation/General/Reference/InfoPlistKeyReference/Articles/LaunchServicesKeys.html#//apple_ref/doc/uid/**********-SW8).", "type": ["null", "string"]}, "compression": {"anyOf": [{"enum": ["maximum", "normal", "store"], "type": "string"}, {"type": "null"}], "default": "normal", "description": "The compression level. If you want to rapidly test build, `store` can reduce build time significantly. `maximum` doesn't lead to noticeable size difference, but increase build time."}, "cscInstallerKeyPassword": {"type": ["null", "string"]}, "cscInstallerLink": {"type": ["null", "string"]}, "cscKeyPassword": {"type": ["null", "string"]}, "cscLink": {"type": ["null", "string"]}, "darkModeSupport": {"default": false, "description": "Whether a dark mode is supported. If your app does have a dark mode, you can make your app follow the system-wide dark mode setting.", "type": "boolean"}, "defaultArch": {"type": "string"}, "detectUpdateChannel": {"default": true, "description": "Whether to infer update channel from application version pre-release components. e.g. if version `0.12.1-alpha.1`, channel will be set to `alpha`. Otherwise to `latest`.\nThis does *not* apply to github publishing, which will [never auto-detect the update channel](https://github.com/electron-userland/electron-builder/issues/8589).", "type": "boolean"}, "disableDefaultIgnoredFiles": {"default": false, "description": "Whether to exclude all default ignored files(https://www.electron.build/contents#files) and options. Defaults to `false`.", "type": ["null", "boolean"]}, "electronLanguages": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "The electron locales to keep. By default, all Electron locales used as-is."}, "electronUpdaterCompatibility": {"description": "The [electron-updater compatibility](./auto-update.md#compatibility) semver range.", "type": ["null", "string"]}, "entitlements": {"description": "The path to entitlements file for signing the app. `build/entitlements.mac.plist` will be used if exists (it is a recommended way to set).\nMAS entitlements is specified in the [mas](./mas.md).\nSee [this folder in osx-sign's repository](https://github.com/electron/osx-sign/tree/main/entitlements) for examples.\nBe aware that your app may crash if the right entitlements are not set like `com.apple.security.cs.allow-jit` for example on arm64 builds with Electron 20+.\nSee [Signing and Notarizing macOS Builds from the Electron documentation](https://www.electronjs.org/docs/latest/tutorial/code-signing#signing--notarizing-macos-builds) for more information.", "type": ["null", "string"]}, "entitlementsInherit": {"description": "The path to child entitlements which inherit the security settings for signing frameworks and bundles of a distribution. `build/entitlements.mac.inherit.plist` will be used if exists (it is a recommended way to set).\nSee [this folder in osx-sign's repository](https://github.com/electron/osx-sign/tree/main/entitlements) for examples.\n\nThis option only applies when signing with `entitlements` provided.", "type": ["null", "string"]}, "entitlementsLoginHelper": {"description": "Path to login helper entitlement file.\nWhen using App Sandbox, the the `com.apple.security.inherit` key that is normally in the inherited entitlements cannot be inherited since the login helper is a standalone executable.\nDefaults to the value provided for `entitlements`. This option only applies when signing with `entitlements` provided.", "type": ["null", "string"]}, "executableName": {"description": "The executable name. Defaults to `productName`.", "type": ["null", "string"]}, "extendInfo": {"description": "The extra entries for `Info.plist`."}, "extraDistFiles": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "Extra files to put in archive. Not applicable for `tar.*`."}, "extraFiles": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"items": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "description": "The same as [extraResources](#extraresources) but copy into the app's content directory (`Contents` for MacOS, root directory for Linux and Windows)."}, "extraResources": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"items": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "description": "A [glob patterns](./file-patterns.md) relative to the project directory, when specified, copy the file or directory with matching names directly into the app's resources directory (`Contents/Resources` for MacOS, `resources` for Linux and Windows).\n\nFile patterns (and support for `from` and `to` fields) the same as for [files](#files)."}, "fileAssociations": {"anyOf": [{"$ref": "#/definitions/FileAssociation"}, {"items": {"$ref": "#/definitions/FileAssociation"}, "type": "array"}], "description": "The file associations."}, "files": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"items": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "description": "A [glob patterns](./file-patterns.md) relative to the [app directory](configuration.md#directories), which specifies which files to include when copying files to create the package.\n\nDefaults to:\n```json\n[\n\"**\\/*\",\n\"!**\\/node_modules/*\\/{CHANGELOG.md,README.md,README,readme.md,readme}\",\n\"!**\\/node_modules/*\\/{test,__tests__,tests,powered-test,example,examples}\",\n\"!**\\/node_modules/*.d.ts\",\n\"!**\\/node_modules/.bin\",\n\"!**\\/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}\",\n\"!.editorconfig\",\n\"!**\\/._*\",\n\"!**\\/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}\",\n\"!**\\/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}\",\n\"!**\\/{appveyor.yml,.travis.yml,circle.yml}\",\n\"!**\\/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}\"\n]\n```\n\nDevelopment dependencies are never copied in any case. You don't need to ignore it explicitly. Hidden files are not ignored by default, but all files that should be ignored, are ignored by default.\n\nDefault pattern \\`**\\/*\\` **is not added to your custom** if some of your patterns is not ignore (i.e. not starts with `!`). `package.json` and \\`**\\/node_modules/**\\/*` (only production dependencies will be copied) is added to your custom in any case. All default ignores are added in any case — you don't need to repeat it if you configure own patterns.\n\nMay be specified in the platform options (e.g. in the [mac](mac.md)).\n\nYou may also specify custom source and destination directories by using `FileSet` objects instead of simple glob patterns.\n\n```json\n[\n{\n\"from\": \"path/to/source\",\n\"to\": \"path/to/destination\",\n\"filter\": [\"**\\/*\", \"!foo/*.js\"]\n}\n]\n```\n\nYou can use [file macros](./file-patterns.md#file-macros) in the `from` and `to` fields as well. `from` and `to` can be files and you can use this to [rename](https://github.com/electron-userland/electron-builder/issues/1119) a file while packaging."}, "forceCodeSigning": {"description": "Whether to fail if app will be not code signed.", "type": "boolean"}, "gatekeeperAssess": {"default": false, "description": "Whether to let `@electron/osx-sign` validate the signing or not.", "type": "boolean"}, "generateUpdatesFilesForAllChannels": {"default": false, "description": "Please see [Building and Releasing using Channels](https://github.com/electron-userland/electron-builder/issues/1182#issuecomment-324947139).", "type": "boolean"}, "hardenedRuntime": {"default": true, "description": "Whether your app has to be signed with hardened runtime.", "type": "boolean"}, "helperBundleId": {"default": "${appBundleIdentifier}.helper", "description": "The bundle identifier to use in the application helper's plist.", "type": ["null", "string"]}, "helperEHBundleId": {"default": "${appBundleIdentifier}.helper.EH", "description": "The bundle identifier to use in the EH helper's plist.", "type": ["null", "string"]}, "helperGPUBundleId": {"default": "${appBundleIdentifier}.helper.GPU", "description": "The bundle identifier to use in the GPU helper's plist.", "type": ["null", "string"]}, "helperNPBundleId": {"default": "${appBundleIdentifier}.helper.NP", "description": "The bundle identifier to use in the NP helper's plist.", "type": ["null", "string"]}, "helperPluginBundleId": {"default": "${appBundleIdentifier}.helper.Plugin", "description": "The bundle identifier to use in the Plugin helper's plist.", "type": ["null", "string"]}, "helperRendererBundleId": {"default": "${appBundleIdentifier}.helper.Renderer", "description": "The bundle identifier to use in the <PERSON><PERSON><PERSON> helper's plist.", "type": ["null", "string"]}, "icon": {"default": "build/icon.icns", "description": "The path to application icon.", "type": ["null", "string"]}, "identity": {"description": "The name of certificate to use when signing. Consider using environment variables [CSC_LINK or CSC_NAME](./code-signing.md) instead of specifying this option.\nMAS installer identity is specified in the [mas](./mas.md).", "type": ["null", "string"]}, "mergeASARs": {"default": true, "description": "Whether to merge ASAR files for different architectures or not.\n\nThis option has no effect unless building for \"universal\" arch.", "type": "boolean"}, "minimumSystemVersion": {"description": "The minimum version of macOS required for the app to run. Corresponds to `LSMinimumSystemVersion`.", "type": ["null", "string"]}, "notarize": {"description": "Whether to disable electron-builder's [@electron/notarize](https://github.com/electron/notarize) integration.\n\nNote: In order to activate the notarization step You MUST specify one of the following via environment variables:\n\n1. `APPLE_API_KEY`, `APPLE_API_KEY_ID` and `APPLE_API_ISSUER`.\n2. `APPLE_ID`, `APPLE_APP_SPECIFIC_PASSWORD`, and `APPLE_TEAM_ID`\n3. `APPLE_KEYCHAIN` and `APPLE_KEYCHAIN_PROFILE`\n\nFor security reasons it is recommended to use the first option (see https://github.com/electron-userland/electron-builder/issues/7859)", "type": "boolean"}, "preAutoEntitlements": {"default": true, "description": "Whether to enable entitlements automation from `@electron/osx-sign`.", "type": "boolean"}, "protocols": {"anyOf": [{"$ref": "#/definitions/Protocol"}, {"items": {"$ref": "#/definitions/Protocol"}, "type": "array"}], "description": "The URL protocol schemes."}, "provisioningProfile": {"description": "The path to the provisioning profile to use when signing, absolute or relative to the app root.", "type": ["null", "string"]}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}], "description": "Publisher configuration. See [Auto Update](./publish.md) for more information."}, "releaseInfo": {"$ref": "#/definitions/ReleaseInfo", "description": "The release info. Intended for command line usage:\n\n```\n-c.releaseInfo.releaseNotes=\"new features\"\n```"}, "requirements": {"description": "Path of [requirements file](https://developer.apple.com/library/mac/documentation/Security/Conceptual/CodeSigningGuide/RequirementLang/RequirementLang.html) used in signing. Not applicable for MAS.", "type": ["null", "string"]}, "sign": {"anyOf": [{"typeof": "function"}, {"type": ["null", "string"]}], "description": "The custom function (or path to file or module id) to sign an app bundle."}, "signIgnore": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "Regex or an array of regex's that signal skipping signing a file."}, "singleArchFiles": {"description": "Minimatch pattern of paths that are allowed to be present in one of the\nASAR files, but not in the other.\n\nThis option has no effect unless building for \"universal\" arch and applies\nonly if `mergeASARs` is `true`.", "type": ["null", "string"]}, "strictVerify": {"default": true, "description": "Whether to let `@electron/osx-sign` verify the contents or not.", "type": "boolean"}, "target": {"anyOf": [{"$ref": "#/definitions/TargetConfiguration"}, {"items": {"anyOf": [{"$ref": "#/definitions/TargetConfiguration"}, {"enum": ["7z", "default", "dir", "dmg", "mas", "mas-dev", "pkg", "tar.bz2", "tar.gz", "tar.lz", "tar.xz", "zip"], "type": "string"}]}, "type": "array"}, {"enum": ["7z", "default", "dir", "dmg", "mas", "mas-dev", "pkg", "tar.bz2", "tar.gz", "tar.lz", "tar.xz", "zip"], "type": "string"}, {"type": "null"}], "description": "The target package type: list of `default`, `dmg`, `mas`, `mas-dev`, `pkg`, `7z`, `zip`, `tar.xz`, `tar.lz`, `tar.gz`, `tar.bz2`, `dir`. Defaults to `default` (`dmg` and `zip` for Squirrel.Mac). Note: Squirrel.Mac auto update mechanism requires both `dmg` and `zip` to be enabled, even when only `dmg` is used. Disabling `zip` will break auto update in `dmg` packages."}, "timestamp": {"description": "Specify the URL of the timestamp authority server", "type": ["null", "string"]}, "type": {"anyOf": [{"enum": ["development", "distribution"], "type": "string"}, {"type": "null"}], "default": "distribution", "description": "Whether to sign app for development or for distribution."}, "x64ArchFiles": {"description": "Minimatch pattern of paths that are allowed to be x64 binaries in both\nASAR files\n\nThis option has no effect unless building for \"universal\" arch and applies\nonly if `mergeASARs` is `true`.", "type": ["null", "string"]}}, "type": "object"}, "MasConfiguration": {"additionalProperties": false, "properties": {"additionalArguments": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Array of strings specifying additional arguments to pass to the `codesign` command used to sign a specific file.\n\nSome subresources that you may include in your Electron app may need to be signed with --deep, this is not typically safe to apply to the entire Electron app and therefore should be applied to just your file.\nUsage Example: `['--deep']`"}, "appId": {"default": "com.electron.${name}", "description": "The application id. Used as [CFBundleIdentifier](https://developer.apple.com/library/ios/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html#//apple_ref/doc/uid/********-102070) for MacOS and as\n[Application User Model ID](https://msdn.microsoft.com/en-us/library/windows/desktop/dd378459(v=vs.85).aspx) for Windows (NSIS target only, Squirrel.Windows not supported). It is strongly recommended that an explicit ID is set.", "type": ["null", "string"]}, "artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template). Defaults to `${productName}-${version}.${ext}` (some target can have other defaults, see corresponding options).", "type": ["null", "string"]}, "asar": {"anyOf": [{"$ref": "#/definitions/AsarOptions"}, {"type": ["null", "boolean"]}], "default": true, "description": "Whether to package the application's source code into an archive, using [Electron's archive format](http://electron.atom.io/docs/tutorial/application-packaging/).\n\nNode modules, that must be unpacked, will be detected automatically, you don't need to explicitly set [asarUnpack](#asarUnpack) - please file an issue if this doesn't work."}, "asarUnpack": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "A [glob patterns](./file-patterns.md) relative to the [app directory](#directories), which specifies which files to unpack when creating the [asar](http://electron.atom.io/docs/tutorial/application-packaging/) archive."}, "binaries": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Paths of any extra binaries that need to be signed."}, "bundleShortVersion": {"description": "The `CFBundleShortVersionString`. Do not use it unless you need to.", "type": ["null", "string"]}, "bundleVersion": {"description": "The `CFBundleVersion`. Do not use it unless [you need to](https://github.com/electron-userland/electron-builder/issues/565#issuecomment-230678643).", "type": ["null", "string"]}, "category": {"description": "The application category type, as shown in the Finder via *View -> Arrange by Application Category* when viewing the Applications directory.\n\nFor example, `\"category\": \"public.app-category.developer-tools\"` will set the application category to *Developer Tools*.\n\nValid values are listed in [Apple's documentation](https://developer.apple.com/library/ios/documentation/General/Reference/InfoPlistKeyReference/Articles/LaunchServicesKeys.html#//apple_ref/doc/uid/**********-SW8).", "type": ["null", "string"]}, "compression": {"anyOf": [{"enum": ["maximum", "normal", "store"], "type": "string"}, {"type": "null"}], "default": "normal", "description": "The compression level. If you want to rapidly test build, `store` can reduce build time significantly. `maximum` doesn't lead to noticeable size difference, but increase build time."}, "cscInstallerKeyPassword": {"type": ["null", "string"]}, "cscInstallerLink": {"type": ["null", "string"]}, "cscKeyPassword": {"type": ["null", "string"]}, "cscLink": {"type": ["null", "string"]}, "darkModeSupport": {"default": false, "description": "Whether a dark mode is supported. If your app does have a dark mode, you can make your app follow the system-wide dark mode setting.", "type": "boolean"}, "defaultArch": {"type": "string"}, "detectUpdateChannel": {"default": true, "description": "Whether to infer update channel from application version pre-release components. e.g. if version `0.12.1-alpha.1`, channel will be set to `alpha`. Otherwise to `latest`.\nThis does *not* apply to github publishing, which will [never auto-detect the update channel](https://github.com/electron-userland/electron-builder/issues/8589).", "type": "boolean"}, "disableDefaultIgnoredFiles": {"default": false, "description": "Whether to exclude all default ignored files(https://www.electron.build/contents#files) and options. Defaults to `false`.", "type": ["null", "boolean"]}, "electronLanguages": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "The electron locales to keep. By default, all Electron locales used as-is."}, "electronUpdaterCompatibility": {"description": "The [electron-updater compatibility](./auto-update.md#compatibility) semver range.", "type": ["null", "string"]}, "entitlements": {"description": "The path to entitlements file for signing the app. `build/entitlements.mas.plist` will be used if exists (it is a recommended way to set).\nSee [this folder in osx-sign's repository](https://github.com/electron/osx-sign/tree/main/entitlements) for examples.\nBe aware that your app may crash if the right entitlements are not set like `com.apple.security.cs.allow-jit` for example on arm64 builds with Electron 20+.\nSee [Signing and Notarizing macOS Builds from the Electron documentation](https://www.electronjs.org/docs/latest/tutorial/code-signing#signing--notarizing-macos-builds) for more information.", "type": ["null", "string"]}, "entitlementsInherit": {"description": "The path to child entitlements which inherit the security settings for signing frameworks and bundles of a distribution. `build/entitlements.mas.inherit.plist` will be used if exists (it is a recommended way to set).\nSee [this folder in osx-sign's repository](https://github.com/electron/osx-sign/tree/main/entitlements) for examples.", "type": ["null", "string"]}, "entitlementsLoginHelper": {"description": "Path to login helper entitlement file.\nWhen using App Sandbox, the the `com.apple.security.inherit` key that is normally in the inherited entitlements cannot be inherited since the login helper is a standalone executable.\nDefaults to the value provided for `entitlements`. This option only applies when signing with `entitlements` provided.", "type": ["null", "string"]}, "executableName": {"description": "The executable name. Defaults to `productName`.", "type": ["null", "string"]}, "extendInfo": {"description": "The extra entries for `Info.plist`."}, "extraDistFiles": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "Extra files to put in archive. Not applicable for `tar.*`."}, "extraFiles": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"items": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "description": "The same as [extraResources](#extraresources) but copy into the app's content directory (`Contents` for MacOS, root directory for Linux and Windows)."}, "extraResources": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"items": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "description": "A [glob patterns](./file-patterns.md) relative to the project directory, when specified, copy the file or directory with matching names directly into the app's resources directory (`Contents/Resources` for MacOS, `resources` for Linux and Windows).\n\nFile patterns (and support for `from` and `to` fields) the same as for [files](#files)."}, "fileAssociations": {"anyOf": [{"$ref": "#/definitions/FileAssociation"}, {"items": {"$ref": "#/definitions/FileAssociation"}, "type": "array"}], "description": "The file associations."}, "files": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"items": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "description": "A [glob patterns](./file-patterns.md) relative to the [app directory](configuration.md#directories), which specifies which files to include when copying files to create the package.\n\nDefaults to:\n```json\n[\n\"**\\/*\",\n\"!**\\/node_modules/*\\/{CHANGELOG.md,README.md,README,readme.md,readme}\",\n\"!**\\/node_modules/*\\/{test,__tests__,tests,powered-test,example,examples}\",\n\"!**\\/node_modules/*.d.ts\",\n\"!**\\/node_modules/.bin\",\n\"!**\\/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}\",\n\"!.editorconfig\",\n\"!**\\/._*\",\n\"!**\\/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}\",\n\"!**\\/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}\",\n\"!**\\/{appveyor.yml,.travis.yml,circle.yml}\",\n\"!**\\/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}\"\n]\n```\n\nDevelopment dependencies are never copied in any case. You don't need to ignore it explicitly. Hidden files are not ignored by default, but all files that should be ignored, are ignored by default.\n\nDefault pattern \\`**\\/*\\` **is not added to your custom** if some of your patterns is not ignore (i.e. not starts with `!`). `package.json` and \\`**\\/node_modules/**\\/*` (only production dependencies will be copied) is added to your custom in any case. All default ignores are added in any case — you don't need to repeat it if you configure own patterns.\n\nMay be specified in the platform options (e.g. in the [mac](mac.md)).\n\nYou may also specify custom source and destination directories by using `FileSet` objects instead of simple glob patterns.\n\n```json\n[\n{\n\"from\": \"path/to/source\",\n\"to\": \"path/to/destination\",\n\"filter\": [\"**\\/*\", \"!foo/*.js\"]\n}\n]\n```\n\nYou can use [file macros](./file-patterns.md#file-macros) in the `from` and `to` fields as well. `from` and `to` can be files and you can use this to [rename](https://github.com/electron-userland/electron-builder/issues/1119) a file while packaging."}, "forceCodeSigning": {"description": "Whether to fail if app will be not code signed.", "type": "boolean"}, "gatekeeperAssess": {"default": false, "description": "Whether to let `@electron/osx-sign` validate the signing or not.", "type": "boolean"}, "generateUpdatesFilesForAllChannels": {"default": false, "description": "Please see [Building and Releasing using Channels](https://github.com/electron-userland/electron-builder/issues/1182#issuecomment-324947139).", "type": "boolean"}, "hardenedRuntime": {"default": true, "description": "Whether your app has to be signed with hardened runtime.", "type": "boolean"}, "helperBundleId": {"default": "${appBundleIdentifier}.helper", "description": "The bundle identifier to use in the application helper's plist.", "type": ["null", "string"]}, "helperEHBundleId": {"default": "${appBundleIdentifier}.helper.EH", "description": "The bundle identifier to use in the EH helper's plist.", "type": ["null", "string"]}, "helperGPUBundleId": {"default": "${appBundleIdentifier}.helper.GPU", "description": "The bundle identifier to use in the GPU helper's plist.", "type": ["null", "string"]}, "helperNPBundleId": {"default": "${appBundleIdentifier}.helper.NP", "description": "The bundle identifier to use in the NP helper's plist.", "type": ["null", "string"]}, "helperPluginBundleId": {"default": "${appBundleIdentifier}.helper.Plugin", "description": "The bundle identifier to use in the Plugin helper's plist.", "type": ["null", "string"]}, "helperRendererBundleId": {"default": "${appBundleIdentifier}.helper.Renderer", "description": "The bundle identifier to use in the <PERSON><PERSON><PERSON> helper's plist.", "type": ["null", "string"]}, "icon": {"default": "build/icon.icns", "description": "The path to application icon.", "type": ["null", "string"]}, "identity": {"description": "The name of certificate to use when signing. Consider using environment variables [CSC_LINK or CSC_NAME](./code-signing.md) instead of specifying this option.\nMAS installer identity is specified in the [mas](./mas.md).", "type": ["null", "string"]}, "mergeASARs": {"default": true, "description": "Whether to merge ASAR files for different architectures or not.\n\nThis option has no effect unless building for \"universal\" arch.", "type": "boolean"}, "minimumSystemVersion": {"description": "The minimum version of macOS required for the app to run. Corresponds to `LSMinimumSystemVersion`.", "type": ["null", "string"]}, "notarize": {"description": "Whether to disable electron-builder's [@electron/notarize](https://github.com/electron/notarize) integration.\n\nNote: In order to activate the notarization step You MUST specify one of the following via environment variables:\n\n1. `APPLE_API_KEY`, `APPLE_API_KEY_ID` and `APPLE_API_ISSUER`.\n2. `APPLE_ID`, `APPLE_APP_SPECIFIC_PASSWORD`, and `APPLE_TEAM_ID`\n3. `APPLE_KEYCHAIN` and `APPLE_KEYCHAIN_PROFILE`\n\nFor security reasons it is recommended to use the first option (see https://github.com/electron-userland/electron-builder/issues/7859)", "type": "boolean"}, "preAutoEntitlements": {"default": true, "description": "Whether to enable entitlements automation from `@electron/osx-sign`.", "type": "boolean"}, "protocols": {"anyOf": [{"$ref": "#/definitions/Protocol"}, {"items": {"$ref": "#/definitions/Protocol"}, "type": "array"}], "description": "The URL protocol schemes."}, "provisioningProfile": {"description": "The path to the provisioning profile to use when signing, absolute or relative to the app root.", "type": ["null", "string"]}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}], "description": "Publisher configuration. See [Auto Update](./publish.md) for more information."}, "releaseInfo": {"$ref": "#/definitions/ReleaseInfo", "description": "The release info. Intended for command line usage:\n\n```\n-c.releaseInfo.releaseNotes=\"new features\"\n```"}, "requirements": {"description": "Path of [requirements file](https://developer.apple.com/library/mac/documentation/Security/Conceptual/CodeSigningGuide/RequirementLang/RequirementLang.html) used in signing. Not applicable for MAS.", "type": ["null", "string"]}, "sign": {"anyOf": [{"typeof": "function"}, {"type": ["null", "string"]}], "description": "The custom function (or path to file or module id) to sign an app bundle."}, "signIgnore": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "Regex or an array of regex's that signal skipping signing a file."}, "singleArchFiles": {"description": "Minimatch pattern of paths that are allowed to be present in one of the\nASAR files, but not in the other.\n\nThis option has no effect unless building for \"universal\" arch and applies\nonly if `mergeASARs` is `true`.", "type": ["null", "string"]}, "strictVerify": {"default": true, "description": "Whether to let `@electron/osx-sign` verify the contents or not.", "type": "boolean"}, "target": {"anyOf": [{"$ref": "#/definitions/TargetConfiguration"}, {"items": {"anyOf": [{"$ref": "#/definitions/TargetConfiguration"}, {"enum": ["7z", "default", "dir", "dmg", "mas", "mas-dev", "pkg", "tar.bz2", "tar.gz", "tar.lz", "tar.xz", "zip"], "type": "string"}]}, "type": "array"}, {"enum": ["7z", "default", "dir", "dmg", "mas", "mas-dev", "pkg", "tar.bz2", "tar.gz", "tar.lz", "tar.xz", "zip"], "type": "string"}, {"type": "null"}], "description": "The target package type: list of `default`, `dmg`, `mas`, `mas-dev`, `pkg`, `7z`, `zip`, `tar.xz`, `tar.lz`, `tar.gz`, `tar.bz2`, `dir`. Defaults to `default` (`dmg` and `zip` for Squirrel.Mac). Note: Squirrel.Mac auto update mechanism requires both `dmg` and `zip` to be enabled, even when only `dmg` is used. Disabling `zip` will break auto update in `dmg` packages."}, "timestamp": {"description": "Specify the URL of the timestamp authority server", "type": ["null", "string"]}, "type": {"anyOf": [{"enum": ["development", "distribution"], "type": "string"}, {"type": "null"}], "default": "distribution", "description": "Whether to sign app for development or for distribution."}, "x64ArchFiles": {"description": "Minimatch pattern of paths that are allowed to be x64 binaries in both\nASAR files\n\nThis option has no effect unless building for \"universal\" arch and applies\nonly if `mergeASARs` is `true`.", "type": ["null", "string"]}}, "type": "object"}, "MetadataDirectories": {"additionalProperties": false, "properties": {"app": {"description": "The application directory (containing the application package.json), defaults to `app`, `www` or working directory.", "type": ["null", "string"]}, "buildResources": {"default": "build", "description": "The path to build resources.\n\nPlease note — build resources are not packed into the app. If you need to use some files, e.g. as tray icon, please include required files explicitly: `\"files\": [\"**\\/*\", \"build/icon.*\"]`", "type": ["null", "string"]}, "output": {"default": "dist", "description": "The output directory. [File macros](./file-patterns.md#file-macros) are supported.", "type": ["null", "string"]}}, "type": "object"}, "MsiOptions": {"additionalProperties": false, "properties": {"additionalLightArgs": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Any additional arguments to be passed to the light.ext, such as `[\"-cultures:ja-jp\"]`"}, "additionalWixArgs": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Any additional arguments to be passed to the WiX installer compiler, such as `[\"-ext\", \"WixUtilExtension\"]`"}, "artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template).", "type": ["null", "string"]}, "createDesktopShortcut": {"default": true, "description": "Whether to create desktop shortcut. Set to `always` if to recreate also on reinstall (even if removed by user).", "enum": ["always", false, true]}, "createStartMenuShortcut": {"default": true, "description": "Whether to create start menu shortcut.", "type": "boolean"}, "menuCategory": {"default": false, "description": "Whether to create submenu for start menu shortcut and program files directory. If `true`, company name will be used. Or string value.", "type": ["string", "boolean"]}, "oneClick": {"default": true, "description": "One-click installation.", "type": "boolean"}, "perMachine": {"default": false, "description": "Whether to install per all users (per-machine).", "type": "boolean"}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}]}, "runAfterFinish": {"default": true, "description": "Whether to run the installed application after finish. For assisted installer corresponding checkbox will be removed.", "type": "boolean"}, "shortcutName": {"description": "The name that will be used for all shortcuts. Defaults to the application name.", "type": ["null", "string"]}, "upgradeCode": {"description": "The [upgrade code](https://msdn.microsoft.com/en-us/library/windows/desktop/aa372375(v=vs.85).aspx). Optional, by default generated using app id.", "type": ["null", "string"]}, "warningsAsErrors": {"default": true, "description": "If `warningsAsErrors` is `true` (default): treat warnings as errors. If `warningsAsErrors` is `false`: allow warnings.", "type": "boolean"}}, "type": "object"}, "MsiWrappedOptions": {"additionalProperties": false, "properties": {"additionalWixArgs": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Any additional arguments to be passed to the WiX installer compiler, such as `[\"-ext\", \"WixUtilExtension\"]`"}, "artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template).", "type": ["null", "string"]}, "createDesktopShortcut": {"default": true, "description": "Whether to create desktop shortcut. Set to `always` if to recreate also on reinstall (even if removed by user).", "enum": ["always", false, true]}, "createStartMenuShortcut": {"default": true, "description": "Whether to create start menu shortcut.", "type": "boolean"}, "impersonate": {"default": false, "description": "Determines if the wrapped installer should be executed with impersonation", "type": "boolean"}, "menuCategory": {"default": false, "description": "Whether to create submenu for start menu shortcut and program files directory. If `true`, company name will be used. Or string value.", "type": ["string", "boolean"]}, "oneClick": {"type": "boolean"}, "perMachine": {"default": false, "description": "Whether to install per all users (per-machine).", "type": "boolean"}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}]}, "runAfterFinish": {"default": true, "description": "Whether to run the installed application after finish. For assisted installer corresponding checkbox will be removed.", "type": "boolean"}, "shortcutName": {"description": "The name that will be used for all shortcuts. Defaults to the application name.", "type": ["null", "string"]}, "upgradeCode": {"description": "The [upgrade code](https://msdn.microsoft.com/en-us/library/windows/desktop/aa372375(v=vs.85).aspx). Optional, by default generated using app id.", "type": ["null", "string"]}, "warningsAsErrors": {"default": true, "description": "If `warningsAsErrors` is `true` (default): treat warnings as errors. If `warningsAsErrors` is `false`: allow warnings.", "type": "boolean"}, "wrappedInstallerArgs": {"description": "Extra arguments to provide to the wrapped installer (ie: /S for silent install)", "type": ["null", "string"]}}, "type": "object"}, "NsisOptions": {"additionalProperties": false, "properties": {"allowElevation": {"default": true, "description": "*assisted installer only.* Allow requesting for elevation. If false, user will have to restart installer with elevated permissions.", "type": "boolean"}, "allowToChangeInstallationDirectory": {"default": false, "description": "*assisted installer only.* Whether to allow user to change installation directory.", "type": "boolean"}, "artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template). Defaults to `${productName} Setup ${version}.${ext}`.", "type": ["null", "string"]}, "buildUniversalInstaller": {"default": true, "description": "Disable building an universal installer of the archs specified in the target configuration\n*Not supported for nsis-web*", "type": "boolean"}, "createDesktopShortcut": {"default": true, "description": "Whether to create desktop shortcut. Set to `always` if to recreate also on reinstall (even if removed by user).", "enum": ["always", false, true]}, "createStartMenuShortcut": {"default": true, "description": "Whether to create start menu shortcut.", "type": "boolean"}, "customNsisBinary": {"anyOf": [{"$ref": "#/definitions/CustomNsisBinary"}, {"type": "null"}], "description": "Allows you to provide your own `makensis`, such as one with support for debug logging via LogSet and LogText. (Logging also requires option `debugLogging = true`)"}, "deleteAppDataOnUninstall": {"default": false, "description": "*one-click installer only.* Whether to delete app data on uninstall.", "type": "boolean"}, "differentialPackage": {"type": "boolean"}, "displayLanguageSelector": {"default": false, "description": "Whether to display a language selection dialog. Not recommended (by default will be detected using OS language).", "type": "boolean"}, "guid": {"description": "See [GUID vs Application Name](./nsis.md#guid-vs-application-name).", "type": ["null", "string"]}, "include": {"description": "The path to NSIS include script to customize installer. Defaults to `build/installer.nsh`. See [Custom NSIS script](#custom-nsis-script).", "type": ["null", "string"]}, "installerHeader": {"default": "build/installerHeader.bmp", "description": "*assisted installer only.* `<PERSON>UI_HEADERIMAGE`, relative to the [build resources](./contents.md#extraresources) or to the project directory.", "type": ["null", "string"]}, "installerHeaderIcon": {"description": "*one-click installer only.* The path to header icon (above the progress bar), relative to the [build resources](./contents.md#extraresources) or to the project directory.\nDefaults to `build/installerHeaderIcon.ico` or application icon.", "type": ["null", "string"]}, "installerIcon": {"description": "The path to installer icon, relative to the [build resources](./contents.md#extraresources) or to the project directory.\nDefaults to `build/installerIcon.ico` or application icon.", "type": ["null", "string"]}, "installerLanguages": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "The installer languages (e.g. `en_US`, `de_DE`). Change only if you understand what do you do and for what."}, "installerSidebar": {"description": "*assisted installer only.* `MUI_WELCOMEFINISHPAGE_BITMAP`, relative to the [build resources](./contents.md#extraresources) or to the project directory.\nDefaults to `build/installerSidebar.bmp` or `${NSISDIR}\\\\Contrib\\\\Graphics\\\\Wizard\\\\nsis3-metro.bmp`. Image size 164 × 314 pixels.", "type": ["null", "string"]}, "language": {"description": "[LCID Dec](https://msdn.microsoft.com/en-au/goglobal/bb964664.aspx), defaults to `1033`(`English - United States`).", "type": ["null", "string"]}, "license": {"description": "The path to EULA license file. Defaults to `license.txt` or `eula.txt` (or uppercase variants). In addition to `txt`, `rtf` and `html` supported (don't forget to use `target=\"_blank\"` for links).\n\nMultiple license files in different languages are supported — use lang postfix (e.g. `_de`, `_ru`). For example, create files `license_de.txt` and `license_en.txt` in the build resources.\nIf OS language is german, `license_de.txt` will be displayed. See map of [language code to name](https://github.com/meikidd/iso-639-1/blob/master/src/data.js).\n\nAppropriate license file will be selected by user OS language.", "type": ["null", "string"]}, "menuCategory": {"default": false, "description": "Whether to create submenu for start menu shortcut and program files directory. If `true`, company name will be used. Or string value.", "type": ["string", "boolean"]}, "multiLanguageInstaller": {"description": "Whether to create multi-language installer. Defaults to `unicode` option value.", "type": "boolean"}, "oneClick": {"default": true, "description": "Whether to create one-click installer or assisted.", "type": "boolean"}, "packElevateHelper": {"default": true, "description": "Whether to pack the elevate executable (required for electron-updater if per-machine installer used or can be used in the future). Ignored if `perMachine` is set to `true`.", "type": "boolean"}, "perMachine": {"default": false, "description": "Whether to show install mode installer page (choice per-machine or per-user) for assisted installer. Or whether installation always per all users (per-machine).\n\nIf `oneClick` is `true` (default): Whether to install per all users (per-machine).\n\nIf `oneClick` is `false` and `perMachine` is `true`: no install mode installer page, always install per-machine.\n\nIf `oneClick` is `false` and `perMachine` is `false` (default): install mode installer page.", "type": "boolean"}, "preCompressedFileExtensions": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "default": [".avi", ".mov", ".m4v", ".mp4", ".m4p", ".qt", ".mkv", ".webm", ".vmdk"], "description": "The file extension of files that will be not compressed. Applicable only for `extraResources` and `extraFiles` files."}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}]}, "removeDefaultUninstallWelcomePage": {"default": false, "description": "*assisted installer only.* remove the default uninstall welcome page.", "type": "boolean"}, "runAfterFinish": {"default": true, "description": "Whether to run the installed application after finish. For assisted installer corresponding checkbox will be removed.", "type": "boolean"}, "script": {"description": "The path to NSIS script to customize installer. Defaults to `build/installer.nsi`. See [Custom NSIS script](#custom-nsis-script).", "type": ["null", "string"]}, "selectPerMachineByDefault": {"default": false, "description": "Whether to set per-machine or per-user installation as default selection on the install mode installer page.", "type": "boolean"}, "shortcutName": {"description": "The name that will be used for all shortcuts. Defaults to the application name.", "type": ["null", "string"]}, "unicode": {"default": true, "description": "Whether to create [Unicode installer](http://nsis.sourceforge.net/Docs/Chapter1.html#intro-unicode).", "type": "boolean"}, "uninstallDisplayName": {"default": "${productName} ${version}", "description": "The uninstaller display name in the control panel.", "type": "string"}, "uninstallerIcon": {"description": "The path to uninstaller icon, relative to the [build resources](./contents.md#extraresources) or to the project directory.\nDefaults to `build/uninstallerIcon.ico` or application icon.", "type": ["null", "string"]}, "uninstallerSidebar": {"description": "*assisted installer only.* `MUI_UNWELCOMEFINISHPAGE_BITMAP`, relative to the [build resources](./contents.md#extraresources) or to the project directory.\nDefaults to `installerSidebar` option or `build/uninstallerSidebar.bmp` or `build/installerSidebar.bmp` or `${NSISDIR}\\\\Contrib\\\\Graphics\\\\Wizard\\\\nsis3-metro.bmp`", "type": ["null", "string"]}, "useZip": {"default": false, "type": "boolean"}, "warningsAsErrors": {"default": true, "description": "If `warningsAsErrors` is `true` (default): NSIS will treat warnings as errors. If `warningsAsErrors` is `false`: NSIS will allow warnings.", "type": "boolean"}}, "type": "object"}, "NsisWebOptions": {"additionalProperties": false, "description": "Web Installer options.", "properties": {"allowElevation": {"default": true, "description": "*assisted installer only.* Allow requesting for elevation. If false, user will have to restart installer with elevated permissions.", "type": "boolean"}, "allowToChangeInstallationDirectory": {"default": false, "description": "*assisted installer only.* Whether to allow user to change installation directory.", "type": "boolean"}, "appPackageUrl": {"description": "The application package download URL. Optional — by default computed using publish configuration.\n\nURL like `https://example.com/download/latest` allows web installer to be version independent (installer will download latest application package).\nPlease note — it is [full URL](https://github.com/electron-userland/electron-builder/issues/1810#issuecomment-317650878).\n\nCustom `X-Arch` http header is set to `32` or `64`.", "type": ["null", "string"]}, "artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template). Defaults to `${productName} Web Setup ${version}.${ext}`.", "type": ["null", "string"]}, "buildUniversalInstaller": {"const": true, "default": true, "description": "Override for `NsisOptions.buildUniversalInstaller`. nsis-web requires universal installer", "type": "boolean"}, "createDesktopShortcut": {"default": true, "description": "Whether to create desktop shortcut. Set to `always` if to recreate also on reinstall (even if removed by user).", "enum": ["always", false, true]}, "createStartMenuShortcut": {"default": true, "description": "Whether to create start menu shortcut.", "type": "boolean"}, "customNsisBinary": {"anyOf": [{"$ref": "#/definitions/CustomNsisBinary"}, {"type": "null"}], "description": "Allows you to provide your own `makensis`, such as one with support for debug logging via LogSet and LogText. (Logging also requires option `debugLogging = true`)"}, "deleteAppDataOnUninstall": {"default": false, "description": "*one-click installer only.* Whether to delete app data on uninstall.", "type": "boolean"}, "differentialPackage": {"type": "boolean"}, "displayLanguageSelector": {"default": false, "description": "Whether to display a language selection dialog. Not recommended (by default will be detected using OS language).", "type": "boolean"}, "guid": {"description": "See [GUID vs Application Name](./nsis.md#guid-vs-application-name).", "type": ["null", "string"]}, "include": {"description": "The path to NSIS include script to customize installer. Defaults to `build/installer.nsh`. See [Custom NSIS script](#custom-nsis-script).", "type": ["null", "string"]}, "installerHeader": {"default": "build/installerHeader.bmp", "description": "*assisted installer only.* `<PERSON>UI_HEADERIMAGE`, relative to the [build resources](./contents.md#extraresources) or to the project directory.", "type": ["null", "string"]}, "installerHeaderIcon": {"description": "*one-click installer only.* The path to header icon (above the progress bar), relative to the [build resources](./contents.md#extraresources) or to the project directory.\nDefaults to `build/installerHeaderIcon.ico` or application icon.", "type": ["null", "string"]}, "installerIcon": {"description": "The path to installer icon, relative to the [build resources](./contents.md#extraresources) or to the project directory.\nDefaults to `build/installerIcon.ico` or application icon.", "type": ["null", "string"]}, "installerLanguages": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "The installer languages (e.g. `en_US`, `de_DE`). Change only if you understand what do you do and for what."}, "installerSidebar": {"description": "*assisted installer only.* `MUI_WELCOMEFINISHPAGE_BITMAP`, relative to the [build resources](./contents.md#extraresources) or to the project directory.\nDefaults to `build/installerSidebar.bmp` or `${NSISDIR}\\\\Contrib\\\\Graphics\\\\Wizard\\\\nsis3-metro.bmp`. Image size 164 × 314 pixels.", "type": ["null", "string"]}, "language": {"description": "[LCID Dec](https://msdn.microsoft.com/en-au/goglobal/bb964664.aspx), defaults to `1033`(`English - United States`).", "type": ["null", "string"]}, "license": {"description": "The path to EULA license file. Defaults to `license.txt` or `eula.txt` (or uppercase variants). In addition to `txt`, `rtf` and `html` supported (don't forget to use `target=\"_blank\"` for links).\n\nMultiple license files in different languages are supported — use lang postfix (e.g. `_de`, `_ru`). For example, create files `license_de.txt` and `license_en.txt` in the build resources.\nIf OS language is german, `license_de.txt` will be displayed. See map of [language code to name](https://github.com/meikidd/iso-639-1/blob/master/src/data.js).\n\nAppropriate license file will be selected by user OS language.", "type": ["null", "string"]}, "menuCategory": {"default": false, "description": "Whether to create submenu for start menu shortcut and program files directory. If `true`, company name will be used. Or string value.", "type": ["string", "boolean"]}, "multiLanguageInstaller": {"description": "Whether to create multi-language installer. Defaults to `unicode` option value.", "type": "boolean"}, "oneClick": {"default": true, "description": "Whether to create one-click installer or assisted.", "type": "boolean"}, "packElevateHelper": {"default": true, "description": "Whether to pack the elevate executable (required for electron-updater if per-machine installer used or can be used in the future). Ignored if `perMachine` is set to `true`.", "type": "boolean"}, "perMachine": {"default": false, "description": "Whether to show install mode installer page (choice per-machine or per-user) for assisted installer. Or whether installation always per all users (per-machine).\n\nIf `oneClick` is `true` (default): Whether to install per all users (per-machine).\n\nIf `oneClick` is `false` and `perMachine` is `true`: no install mode installer page, always install per-machine.\n\nIf `oneClick` is `false` and `perMachine` is `false` (default): install mode installer page.", "type": "boolean"}, "preCompressedFileExtensions": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "default": [".avi", ".mov", ".m4v", ".mp4", ".m4p", ".qt", ".mkv", ".webm", ".vmdk"], "description": "The file extension of files that will be not compressed. Applicable only for `extraResources` and `extraFiles` files."}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}]}, "removeDefaultUninstallWelcomePage": {"default": false, "description": "*assisted installer only.* remove the default uninstall welcome page.", "type": "boolean"}, "runAfterFinish": {"default": true, "description": "Whether to run the installed application after finish. For assisted installer corresponding checkbox will be removed.", "type": "boolean"}, "script": {"description": "The path to NSIS script to customize installer. Defaults to `build/installer.nsi`. See [Custom NSIS script](#custom-nsis-script).", "type": ["null", "string"]}, "selectPerMachineByDefault": {"default": false, "description": "Whether to set per-machine or per-user installation as default selection on the install mode installer page.", "type": "boolean"}, "shortcutName": {"description": "The name that will be used for all shortcuts. Defaults to the application name.", "type": ["null", "string"]}, "unicode": {"default": true, "description": "Whether to create [Unicode installer](http://nsis.sourceforge.net/Docs/Chapter1.html#intro-unicode).", "type": "boolean"}, "uninstallDisplayName": {"default": "${productName} ${version}", "description": "The uninstaller display name in the control panel.", "type": "string"}, "uninstallerIcon": {"description": "The path to uninstaller icon, relative to the [build resources](./contents.md#extraresources) or to the project directory.\nDefaults to `build/uninstallerIcon.ico` or application icon.", "type": ["null", "string"]}, "uninstallerSidebar": {"description": "*assisted installer only.* `MUI_UNWELCOMEFINISHPAGE_BITMAP`, relative to the [build resources](./contents.md#extraresources) or to the project directory.\nDefaults to `installerSidebar` option or `build/uninstallerSidebar.bmp` or `build/installerSidebar.bmp` or `${NSISDIR}\\\\Contrib\\\\Graphics\\\\Wizard\\\\nsis3-metro.bmp`", "type": ["null", "string"]}, "useZip": {"default": false, "type": "boolean"}, "warningsAsErrors": {"default": true, "description": "If `warningsAsErrors` is `true` (default): NSIS will treat warnings as errors. If `warningsAsErrors` is `false`: NSIS will allow warnings.", "type": "boolean"}}, "type": "object"}, "OutgoingHttpHeaders": {"additionalProperties": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["string", "number"]}]}, "properties": {"accept": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}, "accept-charset": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}, "accept-encoding": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}, "accept-language": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}, "accept-ranges": {"type": "string"}, "access-control-allow-credentials": {"type": "string"}, "access-control-allow-headers": {"type": "string"}, "access-control-allow-methods": {"type": "string"}, "access-control-allow-origin": {"type": "string"}, "access-control-expose-headers": {"type": "string"}, "access-control-max-age": {"type": "string"}, "access-control-request-headers": {"type": "string"}, "access-control-request-method": {"type": "string"}, "age": {"type": "string"}, "allow": {"type": "string"}, "authorization": {"type": "string"}, "cache-control": {"type": "string"}, "cdn-cache-control": {"type": "string"}, "connection": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}, "content-disposition": {"type": "string"}, "content-encoding": {"type": "string"}, "content-language": {"type": "string"}, "content-length": {"type": ["string", "number"]}, "content-location": {"type": "string"}, "content-range": {"type": "string"}, "content-security-policy": {"type": "string"}, "content-security-policy-report-only": {"type": "string"}, "content-type": {"type": "string"}, "cookie": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}, "date": {"type": "string"}, "dav": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}, "dnt": {"type": "string"}, "etag": {"type": "string"}, "expect": {"type": "string"}, "expires": {"type": "string"}, "forwarded": {"type": "string"}, "from": {"type": "string"}, "host": {"type": "string"}, "if-match": {"type": "string"}, "if-modified-since": {"type": "string"}, "if-none-match": {"type": "string"}, "if-range": {"type": "string"}, "if-unmodified-since": {"type": "string"}, "last-modified": {"type": "string"}, "link": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}, "location": {"type": "string"}, "max-forwards": {"type": "string"}, "origin": {"type": "string"}, "pragma": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}, "proxy-authenticate": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}, "proxy-authorization": {"type": "string"}, "public-key-pins": {"type": "string"}, "public-key-pins-report-only": {"type": "string"}, "range": {"type": "string"}, "referer": {"type": "string"}, "referrer-policy": {"type": "string"}, "refresh": {"type": "string"}, "retry-after": {"type": "string"}, "sec-websocket-accept": {"type": "string"}, "sec-websocket-extensions": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}, "sec-websocket-key": {"type": "string"}, "sec-websocket-protocol": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}, "sec-websocket-version": {"type": "string"}, "server": {"type": "string"}, "set-cookie": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}, "strict-transport-security": {"type": "string"}, "te": {"type": "string"}, "trailer": {"type": "string"}, "transfer-encoding": {"type": "string"}, "upgrade": {"type": "string"}, "upgrade-insecure-requests": {"type": "string"}, "user-agent": {"type": "string"}, "vary": {"type": "string"}, "via": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}, "warning": {"type": "string"}, "www-authenticate": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}, "x-content-type-options": {"type": "string"}, "x-dns-prefetch-control": {"type": "string"}, "x-frame-options": {"type": "string"}, "x-xss-protection": {"type": "string"}}, "type": "object"}, "PkgBackgroundOptions": {"additionalProperties": false, "description": "Options for the background image in a PKG installer", "properties": {"alignment": {"anyOf": [{"enum": ["bottom", "bottomleft", "bottomright", "center", "left", "right", "top", "topleft", "topright"], "type": "string"}, {"type": "null"}], "default": "center", "description": "Alignment of the background image.\nOptions are: center, left, right, top, bottom, topleft, topright, bottomleft, bottomright"}, "file": {"description": "Path to the image to use as an installer background.", "type": "string"}, "scaling": {"anyOf": [{"enum": ["none", "proportional", "tofit"], "type": "string"}, {"type": "null"}], "default": "tofit", "description": "Scaling of the background image.\nOptions are: tofit, none, proportional"}}, "type": "object"}, "PkgOptions": {"additionalProperties": false, "description": "macOS product archive options.", "properties": {"allowAnywhere": {"default": true, "description": "Whether can be installed at the root of any volume, including non-system volumes. Otherwise, it cannot be installed at the root of a volume.\n\nCorresponds to [enable_anywhere](https://developer.apple.com/library/content/documentation/DeveloperTools/Reference/DistributionDefinitionRef/Chapters/Distribution_XML_Ref.html#//apple_ref/doc/uid/**********-CH100-SW70).", "type": ["null", "boolean"]}, "allowCurrentUserHome": {"default": true, "description": "Whether can be installed into the current user’s home directory.\nA home directory installation is done as the current user (not as root), and it cannot write outside of the home directory.\nIf the product cannot be installed in the user’s home directory and be not completely functional from user’s home directory.\n\nCorresponds to [enable_currentUserHome](https://developer.apple.com/library/content/documentation/DeveloperTools/Reference/DistributionDefinitionRef/Chapters/Distribution_XML_Ref.html#//apple_ref/doc/uid/**********-CH100-SW70).", "type": ["null", "boolean"]}, "allowRootDirectory": {"default": true, "description": "Whether can be installed into the root directory. Should usually be `true` unless the product can be installed only to the user’s home directory.\n\nCorresponds to [enable_localSystem](https://developer.apple.com/library/content/documentation/DeveloperTools/Reference/DistributionDefinitionRef/Chapters/Distribution_XML_Ref.html#//apple_ref/doc/uid/**********-CH100-SW70).", "type": ["null", "boolean"]}, "artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template).", "type": ["null", "string"]}, "background": {"anyOf": [{"$ref": "#/definitions/PkgBackgroundOptions"}, {"type": "null"}], "description": "Options for the background image for the installer."}, "conclusion": {"description": "The path to the conclusion file. This may be used to customize the text on the final \"Summary\" page of the installer.", "type": ["null", "string"]}, "extraPkgsDir": {"description": "The extra component packages directory (relative to build resources directory) for MacOS product archive\nAutoscans directory for any `.pkg` files and adds to `productbuild` command as `--package-path` and `--package` accordingly", "type": ["null", "string"]}, "hasStrictIdentifier": {"default": true, "description": "Require identical bundle identifiers at install path?", "type": ["null", "boolean"]}, "identity": {"description": "The name of certificate to use when signing. Consider using environment variables [CSC_LINK or CSC_NAME](./code-signing.md) instead of specifying this option.", "type": ["null", "string"]}, "installLocation": {"default": "/Applications", "description": "The install location. [Do not use it](https://stackoverflow.com/questions/12863944/how-do-you-specify-a-default-install-location-to-home-with-pkgbuild) to create per-user package.\nMostly never you will need to change this option. `/Applications` would install it as expected into `/Applications` if the local system domain is chosen, or into `$HOME/Applications` if the home installation is chosen.", "type": ["null", "string"]}, "isRelocatable": {"default": true, "description": "Install bundle over previous version if moved by user?", "type": ["null", "boolean"]}, "isVersionChecked": {"default": true, "description": "Don't install bundle if newer version on disk?", "type": ["null", "boolean"]}, "license": {"description": "The path to EULA license file. Defaults to `license.txt` or `eula.txt` (or uppercase variants). In addition to `txt`, `rtf` and `html` supported (don't forget to use `target=\"_blank\"` for links).", "type": ["null", "string"]}, "mustClose": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Identifies applications that must be closed before the package is installed.\n\nCorresponds to [must-close](https://developer.apple.com/library/archive/documentation/DeveloperTools/Reference/DistributionDefinitionRef/Chapters/Distribution_XML_Ref.html#//apple_ref/doc/uid/**********-CH100-SW77)."}, "overwriteAction": {"anyOf": [{"enum": ["update", "upgrade"], "type": "string"}, {"type": "null"}], "default": "upgrade", "description": "Specifies how an existing version of the bundle on disk should be handled when the version in\nthe package is installed.\n\nIf you specify upgrade, the bundle in the package atomi-cally replaces any version on disk;\nthis has the effect of deleting old paths that no longer exist in the new version of\nthe bundle.\n\nIf you specify update, the bundle in the package overwrites the version on disk, and any files\nnot contained in the package will be left intact; this is appropriate when you are delivering\nan update-only package.\n\nAnother effect of update is that the package bundle will not be installed at all if there is\nnot already a version on disk; this allows a package to deliver an update for an app that\nthe user might have deleted."}, "productbuild": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "should be not documented, only to experiment"}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}]}, "scripts": {"default": "build/pkg-scripts", "description": "The scripts directory, relative to `build` (build resources directory).\nThe scripts can be in any language so long as the files are marked executable and have the appropriate shebang indicating the path to the interpreter.\nScripts are required to be executable (`chmod +x file`).", "type": ["null", "string"]}, "welcome": {"description": "The path to the welcome file. This may be used to customize the text on the Introduction page of the installer.", "type": ["null", "string"]}}, "type": "object"}, "PlugDescriptor": {"additionalProperties": {"anyOf": [{"type": "object"}, {"type": "null"}]}, "type": "object"}, "PortableOptions": {"additionalProperties": false, "description": "Portable options.", "properties": {"artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template).", "type": ["null", "string"]}, "customNsisBinary": {"anyOf": [{"$ref": "#/definitions/CustomNsisBinary"}, {"type": "null"}], "description": "Allows you to provide your own `makensis`, such as one with support for debug logging via LogSet and LogText. (Logging also requires option `debugLogging = true`)"}, "guid": {"description": "See [GUID vs Application Name](./nsis.md#guid-vs-application-name).", "type": ["null", "string"]}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}]}, "requestExecutionLevel": {"default": "user", "description": "The [requested execution level](http://nsis.sourceforge.net/Reference/RequestExecutionLevel) for Windows.", "enum": ["admin", "highest", "user"], "type": "string"}, "splashImage": {"description": "The image to show while the portable executable is extracting. This image must be a bitmap (`.bmp`) image.", "type": ["null", "string"]}, "unicode": {"default": true, "description": "Whether to create [Unicode installer](http://nsis.sourceforge.net/Docs/Chapter1.html#intro-unicode).", "type": "boolean"}, "unpackDirName": {"description": "The unpack directory for the portable app resources.\n\nIf set to a string, it will be the name in [TEMP](https://www.askvg.com/where-does-windows-store-temporary-files-and-how-to-change-temp-folder-location/) directory\nIf set explicitly to `false`, it will use the Windows temp directory ($PLUGINSDIR) that is unique to each launch of the portable application.\n\nDefaults to [uuid](https://github.com/segmentio/ksuid) of build (changed on each build of portable executable).", "type": ["string", "boolean"]}, "useZip": {"default": false, "type": "boolean"}, "warningsAsErrors": {"default": true, "description": "If `warningsAsErrors` is `true` (default): NSIS will treat warnings as errors. If `warningsAsErrors` is `false`: NSIS will allow warnings.", "type": "boolean"}}, "type": "object"}, "Protocol": {"additionalProperties": false, "description": "URL Protocol Schemes. Protocols to associate the app with. macOS only.\n\nPlease note — on macOS [you need to register an `open-url` event handler](http://electron.atom.io/docs/api/app/#event-open-url-macos).", "properties": {"name": {"description": "The name. e.g. `IRC server URL`.", "type": "string"}, "role": {"default": "Editor", "description": "*macOS-only* The app’s role with respect to the type.", "enum": ["Editor", "None", "Shell", "Viewer"], "type": "string"}, "schemes": {"description": "The schemes. e.g. `[\"irc\", \"ircs\"]`.", "items": {"type": "string"}, "type": "array"}}, "required": ["name", "schemes"], "type": "object"}, "ReleaseInfo": {"additionalProperties": false, "properties": {"releaseDate": {"description": "The release date.", "type": "string"}, "releaseName": {"description": "The release name.", "type": ["null", "string"]}, "releaseNotes": {"description": "The release notes.", "type": ["null", "string"]}, "releaseNotesFile": {"description": "The path to release notes file. Defaults to `release-notes-${platform}.md` (where `platform` it is current platform — `mac`, `linux` or `windows`) or `release-notes.md` in the [build resources](./contents.md#extraresources).", "type": ["null", "string"]}, "vendor": {"anyOf": [{"typeof": "function"}, {"type": "null"}], "description": "Vendor specific information."}}, "type": "object"}, "S3Options": {"additionalProperties": false, "description": "[Amazon S3](https://aws.amazon.com/s3/) options.\nAWS credentials are required, please see [getting your credentials](http://docs.aws.amazon.com/sdk-for-javascript/v2/developer-guide/getting-your-credentials.html).\nDefine `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY` [environment variables](http://docs.aws.amazon.com/sdk-for-javascript/v2/developer-guide/loading-node-credentials-environment.html).\nOr in the [~/.aws/credentials](http://docs.aws.amazon.com/sdk-for-javascript/v2/developer-guide/loading-node-credentials-shared.html).\n\nExample configuration:\n\n```json\n{\n\"build\":\n \"publish\": {\n   \"provider\": \"s3\",\n   \"bucket\": \"bucket-name\"\n }\n}\n}\n```", "properties": {"accelerate": {"description": "If set to true, this will enable the s3 accelerated endpoint\nThese endpoints have a particular format of:\n ${bucketname}.s3-accelerate.amazonaws.com", "type": "boolean"}, "acl": {"anyOf": [{"enum": ["private", "public-read"], "type": "string"}, {"type": "null"}], "default": "public-read", "description": "The ACL. Set to `null` to not [add](https://github.com/electron-userland/electron-builder/issues/1822).\n\nPlease see [required permissions for the S3 provider](https://github.com/electron-userland/electron-builder/issues/1618#issuecomment-314679128)."}, "bucket": {"description": "The bucket name.", "type": "string"}, "channel": {"default": "latest", "description": "The update channel.", "type": ["null", "string"]}, "encryption": {"anyOf": [{"enum": ["AES256", "aws:kms"], "type": "string"}, {"type": "null"}], "description": "Server-side encryption algorithm to use for the object."}, "endpoint": {"description": "The endpoint URI to send requests to. The default endpoint is built from the configured region.\nThe endpoint should be a string like `https://{service}.{region}.amazonaws.com`.", "type": ["null", "string"]}, "forcePathStyle": {"description": "When true, force a path-style endpoint to be used where the bucket name is part of the path.\n[Path-style Access](https://docs.aws.amazon.com/AmazonS3/latest/userguide/VirtualHosting.html#path-style-access)", "type": "boolean"}, "path": {"default": "/", "description": "The directory path.", "type": ["null", "string"]}, "provider": {"const": "s3", "description": "The provider. Must be `s3`.", "type": "string"}, "publishAutoUpdate": {"default": true, "description": "Whether to publish auto update info files.\n\nAuto update relies only on the first provider in the list (you can specify several publishers).\nThus, probably, there`s no need to upload the metadata files for the other configured providers. But by default will be uploaded.", "type": "boolean"}, "publisherName": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}]}, "region": {"description": "The region. Is determined and set automatically when publishing.", "type": ["null", "string"]}, "requestHeaders": {"$ref": "#/definitions/OutgoingHttpHeaders", "description": "Any custom request headers"}, "storageClass": {"anyOf": [{"enum": ["REDUCED_REDUNDANCY", "STANDARD", "STANDARD_IA"], "type": "string"}, {"type": "null"}], "default": "STANDARD", "description": "The type of storage to use for the object."}, "timeout": {"default": 120000, "description": "Request timeout in milliseconds. (Default is 2 minutes; O is ignored)", "type": ["null", "number"]}, "updaterCacheDirName": {"type": ["null", "string"]}}, "required": ["bucket", "provider"], "type": "object"}, "SlotDescriptor": {"additionalProperties": {"anyOf": [{"typeof": "function"}, {"type": "null"}]}, "type": "object"}, "SnapOptions": {"additionalProperties": false, "properties": {"after": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Specifies any [parts](https://snapcraft.io/docs/reference/parts) that should be built before this part.\nDefaults to `[\"desktop-gtk2\"\"]`.\n\nIf list contains `default`, it will be replaced to default list, so, `[\"default\", \"foo\"]` can be used to add custom parts `foo` in addition to defaults."}, "allowNativeWayland": {"description": "Allow running the program with native wayland support with --ozone-platform=wayland.\nDisabled by default because of this issue in older Electron/Snap versions: https://github.com/electron-userland/electron-builder/issues/4007", "type": ["null", "boolean"]}, "appPartStage": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Specifies which files from the app part to stage and which to exclude. Individual files, directories, wildcards, globstars, and exclusions are accepted. See [Snapcraft filesets](https://snapcraft.io/docs/snapcraft-filesets) to learn more about the format.\n\nThe defaults can be found in [snap.ts](https://github.com/electron-userland/electron-builder/blob/master/packages/app-builder-lib/templates/snap/snapcraft.yaml#L29)."}, "artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template).", "type": ["null", "string"]}, "assumes": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "The list of features that must be supported by the core in order for this snap to install."}, "autoStart": {"default": false, "description": "Whether or not the snap should automatically start on login.", "type": "boolean"}, "base": {"description": "A snap of type base to be used as the execution environment for this snap. Examples: `core`, `core18`, `core20`, `core22`. Defaults to `core20`", "type": ["null", "string"]}, "buildPackages": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "The list of debian packages needs to be installed for building this snap."}, "category": {"description": "The [application category](https://specifications.freedesktop.org/menu-spec/latest/apa.html#main-category-registry).", "type": ["null", "string"]}, "compression": {"anyOf": [{"enum": ["lzo", "xz"], "type": "string"}, {"type": "null"}], "description": "Sets the compression type for the snap. Can be xz, lzo, or null."}, "confinement": {"anyOf": [{"enum": ["classic", "devmode", "strict"], "type": "string"}, {"type": "null"}], "default": "strict", "description": "The type of [confinement](https://snapcraft.io/docs/reference/confinement) supported by the snap."}, "description": {"description": "As [description](./configuration.md#description) from application package.json, but allows you to specify different for Linux.", "type": ["null", "string"]}, "desktop": {"anyOf": [{"$ref": "#/definitions/LinuxDesktopFile"}, {"type": "null"}], "description": "The [Desktop file](https://developer.gnome.org/documentation/guidelines/maintainer/integrating.html#desktop-files)"}, "environment": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "description": "The custom environment. Defaults to `{\"TMPDIR: \"$XDG_RUNTIME_DIR\"}`. If you set custom, it will be merged with default."}, "executableArgs": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "The executable parameters. Pass to executableName"}, "grade": {"anyOf": [{"enum": ["devel", "stable"], "type": "string"}, {"type": "null"}], "default": "stable", "description": "The quality grade of the snap. It can be either `devel` (i.e. a development version of the snap, so not to be published to the “stable” or “candidate” channels) or “stable” (i.e. a stable release or release candidate, which can be released to all channels)."}, "hooks": {"default": "build/snap-hooks", "description": "The [hooks](https://docs.snapcraft.io/build-snaps/hooks) directory, relative to `build` (build resources directory).", "type": ["null", "string"]}, "layout": {"anyOf": [{"typeof": "function"}, {"type": "null"}], "description": "Specifies any files to make accessible from locations such as `/usr`, `/var`, and `/etc`. See [snap layouts](https://snapcraft.io/docs/snap-layouts) to learn more."}, "mimeTypes": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "The mime types in addition to specified in the file associations. Use it if you don't want to register a new mime type, but reuse existing."}, "plugs": {"anyOf": [{"$ref": "#/definitions/PlugDescriptor"}, {"items": {"anyOf": [{"$ref": "#/definitions/PlugDescriptor"}, {"type": "string"}]}, "type": "array"}, {"type": "null"}], "description": "The list of [plugs](https://snapcraft.io/docs/reference/interfaces).\nDefaults to `[\"desktop\", \"desktop-legacy\", \"home\", \"x11\", \"wayland\", \"unity7\", \"browser-support\", \"network\", \"gsettings\", \"audio-playback\", \"pulseaudio\", \"opengl\"]`.\n\nIf list contains `default`, it will be replaced to default list, so, `[\"default\", \"foo\"]` can be used to add custom plug `foo` in addition to defaults.\n\nAdditional attributes can be specified using object instead of just name of plug:\n```\n[\n {\n   \"browser-sandbox\": {\n     \"interface\": \"browser-support\",\n     \"allow-sandbox\": true\n   },\n },\n \"another-simple-plug-name\"\n]\n```"}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}]}, "slots": {"anyOf": [{"$ref": "#/definitions/PlugDescriptor"}, {"items": {"anyOf": [{"$ref": "#/definitions/SlotDescriptor"}, {"type": "string"}]}, "type": "array"}, {"type": "null"}], "description": "The list of [slots](https://snapcraft.io/docs/reference/interfaces).\n\nAdditional attributes can be specified using object instead of just name of slot:\n```\n[\n {\n   \"mpris\": {\n     \"name\": \"chromium\"\n   },\n }\n]\n\nIn case you want your application to be a compliant MPris player, you will need to definie\nThe mpris slot with \"chromium\" name.\nThis electron has it [hardcoded](https://source.chromium.org/chromium/chromium/src/+/master:components/system_media_controls/linux/system_media_controls_linux.cc;l=51;bpv=0;bpt=1),\nand we need to pass this name so snap [will allow it](https://forum.snapcraft.io/t/unable-to-use-mpris-interface/15360/7) in strict confinement."}, "stagePackages": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "The list of Ubuntu packages to use that are needed to support the `app` part creation. Like `depends` for `deb`.\nDefaults to `[\"libnspr4\", \"libnss3\", \"libxss1\", \"libappindicator3-1\", \"libsecret-1-0\"]`.\n\nIf list contains `default`, it will be replaced to default list, so, `[\"default\", \"foo\"]` can be used to add custom package `foo` in addition to defaults."}, "summary": {"description": "The 78 character long summary. Defaults to [productName](./configuration.md#productName).", "type": ["null", "string"]}, "synopsis": {"description": "The [short description](https://www.debian.org/doc/debian-policy/ch-controlfields.html#s-f-Description).", "type": ["null", "string"]}, "title": {"description": "An optional title for the snap, may contain uppercase letters and spaces. Defaults to `productName`. See [snap format documentation](https://snapcraft.io/docs/snap-format).", "type": ["null", "string"]}, "useTemplateApp": {"description": "Whether to use template snap. Defaults to `true` if `stagePackages` not specified.", "type": "boolean"}}, "type": "object"}, "SnapStoreOptions": {"additionalProperties": false, "description": "[Snap Store](https://snapcraft.io/) options. To publish directly to Snapcraft, see <a href=\"https://snapcraft.io/docs/snapcraft-authentication\">Snapcraft authentication options</a> for local or CI/CD authentication options.", "properties": {"channels": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "default": ["edge"], "description": "The list of channels the snap would be released."}, "provider": {"const": "snapStore", "description": "The provider. Must be `snapStore`.", "type": "string"}, "publishAutoUpdate": {"default": true, "description": "Whether to publish auto update info files.\n\nAuto update relies only on the first provider in the list (you can specify several publishers).\nThus, probably, there`s no need to upload the metadata files for the other configured providers. But by default will be uploaded.", "type": "boolean"}, "publisherName": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}]}, "repo": {"description": "snapcraft repo name", "type": "string"}, "requestHeaders": {"$ref": "#/definitions/OutgoingHttpHeaders", "description": "Any custom request headers"}, "timeout": {"default": 120000, "description": "Request timeout in milliseconds. (Default is 2 minutes; O is ignored)", "type": ["null", "number"]}, "updaterCacheDirName": {"type": ["null", "string"]}}, "required": ["provider"], "type": "object"}, "SpacesOptions": {"additionalProperties": false, "description": "[DigitalOcean Spaces](https://www.digitalocean.com/community/tutorials/an-introduction-to-digitalocean-spaces) options.\nAccess key is required, define `DO_KEY_ID` and `DO_SECRET_KEY` environment variables.", "properties": {"acl": {"anyOf": [{"enum": ["private", "public-read"], "type": "string"}, {"type": "null"}], "default": "public-read", "description": "The ACL. Set to `null` to not [add](https://github.com/electron-userland/electron-builder/issues/1822)."}, "channel": {"default": "latest", "description": "The update channel.", "type": ["null", "string"]}, "name": {"description": "The space name.", "type": "string"}, "path": {"default": "/", "description": "The directory path.", "type": ["null", "string"]}, "provider": {"const": "spaces", "description": "The provider. Must be `spaces`.", "type": "string"}, "publishAutoUpdate": {"default": true, "description": "Whether to publish auto update info files.\n\nAuto update relies only on the first provider in the list (you can specify several publishers).\nThus, probably, there`s no need to upload the metadata files for the other configured providers. But by default will be uploaded.", "type": "boolean"}, "publisherName": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}]}, "region": {"description": "The region (e.g. `nyc3`).", "type": "string"}, "requestHeaders": {"$ref": "#/definitions/OutgoingHttpHeaders", "description": "Any custom request headers"}, "timeout": {"default": 120000, "description": "Request timeout in milliseconds. (Default is 2 minutes; O is ignored)", "type": ["null", "number"]}, "updaterCacheDirName": {"type": ["null", "string"]}}, "required": ["name", "provider", "region"], "type": "object"}, "SquirrelWindowsOptions": {"additionalProperties": false, "properties": {"artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template).", "type": ["null", "string"]}, "customSquirrelVendorDir": {"description": "The custom squirrel vendor dir. If not specified will use the Squirrel.Windows that is shipped with electron-installer(https://github.com/electron/windows-installer/tree/main/vendor).\nAfter https://github.com/electron-userland/electron-builder-binaries/pull/56 merged, will add `electron-builder-binaries` to get the latest version of squirrel.", "type": "string"}, "iconUrl": {"description": "A URL to an ICO file to use as the application icon (displayed in Control Panel > Programs and Features). Defaults to the Electron icon.\n\nPlease note — [local icon file url is not accepted](https://github.com/atom/grunt-electron-installer/issues/73), must be https/http.\n\nIf you don't plan to build windows installer, you can omit it.\nIf your project repository is public on GitHub, it will be `https://github.com/${u}/${p}/blob/master/build/icon.ico?raw=true` by default.", "type": ["null", "string"]}, "loadingGif": {"description": "The path to a .gif file to display during install. `build/install-spinner.gif` will be used if exists (it is a recommended way to set)\n(otherwise [default](https://github.com/electron/windows-installer/blob/master/resources/install-spinner.gif)).", "type": ["null", "string"]}, "msi": {"description": "Whether to create an MSI installer. Defaults to `false` (MSI is not created).", "type": "boolean"}, "name": {"description": "https://github.com/electron-userland/electron-builder/issues/1743", "type": "string"}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}]}, "remoteReleases": {"description": "A URL to your existing updates. Or `true` to automatically set to your GitHub repository. If given, these will be downloaded to create delta updates.", "type": ["null", "string", "boolean"]}, "remoteToken": {"description": "Authentication token for remote updates", "type": ["null", "string"]}, "useAppIdAsId": {"description": "Use `appId` to identify package instead of `name`.", "type": "boolean"}}, "type": "object"}, "TargetConfiguration": {"additionalProperties": false, "properties": {"arch": {"anyOf": [{"items": {"$ref": "#/definitions/ArchType"}, "type": "array"}, {"enum": ["arm64", "armv7l", "ia32", "universal", "x64"], "type": "string"}], "description": "The arch or list of archs."}, "target": {"description": "The target name. e.g. `snap`.", "type": "string"}}, "required": ["target"], "type": "object"}, "WindowsAzureSigningConfiguration": {"additionalProperties": {"type": ["null", "string"]}, "properties": {"certificateProfileName": {"description": "The Certificate Profile name. Translates to field: CertificateProfileName", "type": "string"}, "codeSigningAccountName": {"description": "The Code Signing Signing Account name. Translates to field: CodeSigningAccountName", "type": "string"}, "endpoint": {"description": "The Trusted Signing Account endpoint. The URI value must have a URI that aligns to the\nregion your Trusted Signing Account and Certificate Profile you are specifying were created\nin during the setup of these resources.\n\nTranslates to field: Endpoint\n\nRequires one of environment variable configurations for authenticating to Microsoft Entra ID per [Microsoft's documentation](https://learn.microsoft.com/en-us/dotnet/api/azure.identity.environmentcredential?view=azure-dotnet#definition)", "type": "string"}, "fileDigest": {"default": "SHA256", "description": "The File Digest for signing each file. Translates to field: FileDigest", "type": "string"}, "publisherName": {"description": "[The publisher name](https://github.com/electron-userland/electron-builder/issues/1187#issuecomment-*********), exactly as in your code signed certificate. Several names can be provided.", "type": "string"}, "timestampDigest": {"default": "SHA256", "description": "The Timestamp Digest. Translates to field: TimestampDigest", "type": "string"}, "timestampRfc3161": {"default": "http://timestamp.acs.microsoft.com", "description": "The Timestamp rfc3161 server. Translates to field: TimestampRfc3161", "type": "string"}}, "required": ["certificateProfileName", "codeSigningAccountName", "endpoint", "publisherName"], "type": "object"}, "WindowsConfiguration": {"additionalProperties": false, "properties": {"appId": {"default": "com.electron.${name}", "description": "The application id. Used as [CFBundleIdentifier](https://developer.apple.com/library/ios/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html#//apple_ref/doc/uid/********-102070) for MacOS and as\n[Application User Model ID](https://msdn.microsoft.com/en-us/library/windows/desktop/dd378459(v=vs.85).aspx) for Windows (NSIS target only, Squirrel.Windows not supported). It is strongly recommended that an explicit ID is set.", "type": ["null", "string"]}, "artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template). Defaults to `${productName}-${version}.${ext}` (some target can have other defaults, see corresponding options).", "type": ["null", "string"]}, "asar": {"anyOf": [{"$ref": "#/definitions/AsarOptions"}, {"type": ["null", "boolean"]}], "default": true, "description": "Whether to package the application's source code into an archive, using [Electron's archive format](http://electron.atom.io/docs/tutorial/application-packaging/).\n\nNode modules, that must be unpacked, will be detected automatically, you don't need to explicitly set [asarUnpack](#asarUnpack) - please file an issue if this doesn't work."}, "asarUnpack": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "A [glob patterns](./file-patterns.md) relative to the [app directory](#directories), which specifies which files to unpack when creating the [asar](http://electron.atom.io/docs/tutorial/application-packaging/) archive."}, "azureSignOptions": {"anyOf": [{"$ref": "#/definitions/WindowsAzureSigningConfiguration"}, {"type": "null"}], "description": "Options for usage of Azure Trusted Signing (beta)\nCannot be used in conjunction with `signtoolOptions`, signing will default to Azure Trusted Signing"}, "compression": {"anyOf": [{"enum": ["maximum", "normal", "store"], "type": "string"}, {"type": "null"}], "default": "normal", "description": "The compression level. If you want to rapidly test build, `store` can reduce build time significantly. `maximum` doesn't lead to noticeable size difference, but increase build time."}, "cscKeyPassword": {"type": ["null", "string"]}, "cscLink": {"type": ["null", "string"]}, "defaultArch": {"type": "string"}, "detectUpdateChannel": {"default": true, "description": "Whether to infer update channel from application version pre-release components. e.g. if version `0.12.1-alpha.1`, channel will be set to `alpha`. Otherwise to `latest`.\nThis does *not* apply to github publishing, which will [never auto-detect the update channel](https://github.com/electron-userland/electron-builder/issues/8589).", "type": "boolean"}, "disableDefaultIgnoredFiles": {"default": false, "description": "Whether to exclude all default ignored files(https://www.electron.build/contents#files) and options. Defaults to `false`.", "type": ["null", "boolean"]}, "electronLanguages": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "The electron locales to keep. By default, all Electron locales used as-is."}, "electronUpdaterCompatibility": {"description": "The [electron-updater compatibility](./auto-update.md#compatibility) semver range.", "type": ["null", "string"]}, "executableName": {"description": "The executable name. Defaults to `productName`.", "type": ["null", "string"]}, "extraFiles": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"items": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "description": "The same as [extraResources](#extraresources) but copy into the app's content directory (`Contents` for MacOS, root directory for Linux and Windows)."}, "extraResources": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"items": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "description": "A [glob patterns](./file-patterns.md) relative to the project directory, when specified, copy the file or directory with matching names directly into the app's resources directory (`Contents/Resources` for MacOS, `resources` for Linux and Windows).\n\nFile patterns (and support for `from` and `to` fields) the same as for [files](#files)."}, "fileAssociations": {"anyOf": [{"$ref": "#/definitions/FileAssociation"}, {"items": {"$ref": "#/definitions/FileAssociation"}, "type": "array"}], "description": "The file associations."}, "files": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"items": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "description": "A [glob patterns](./file-patterns.md) relative to the [app directory](configuration.md#directories), which specifies which files to include when copying files to create the package.\n\nDefaults to:\n```json\n[\n\"**\\/*\",\n\"!**\\/node_modules/*\\/{CHANGELOG.md,README.md,README,readme.md,readme}\",\n\"!**\\/node_modules/*\\/{test,__tests__,tests,powered-test,example,examples}\",\n\"!**\\/node_modules/*.d.ts\",\n\"!**\\/node_modules/.bin\",\n\"!**\\/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}\",\n\"!.editorconfig\",\n\"!**\\/._*\",\n\"!**\\/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}\",\n\"!**\\/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}\",\n\"!**\\/{appveyor.yml,.travis.yml,circle.yml}\",\n\"!**\\/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}\"\n]\n```\n\nDevelopment dependencies are never copied in any case. You don't need to ignore it explicitly. Hidden files are not ignored by default, but all files that should be ignored, are ignored by default.\n\nDefault pattern \\`**\\/*\\` **is not added to your custom** if some of your patterns is not ignore (i.e. not starts with `!`). `package.json` and \\`**\\/node_modules/**\\/*` (only production dependencies will be copied) is added to your custom in any case. All default ignores are added in any case — you don't need to repeat it if you configure own patterns.\n\nMay be specified in the platform options (e.g. in the [mac](mac.md)).\n\nYou may also specify custom source and destination directories by using `FileSet` objects instead of simple glob patterns.\n\n```json\n[\n{\n\"from\": \"path/to/source\",\n\"to\": \"path/to/destination\",\n\"filter\": [\"**\\/*\", \"!foo/*.js\"]\n}\n]\n```\n\nYou can use [file macros](./file-patterns.md#file-macros) in the `from` and `to` fields as well. `from` and `to` can be files and you can use this to [rename](https://github.com/electron-userland/electron-builder/issues/1119) a file while packaging."}, "forceCodeSigning": {"description": "Whether to fail if app will be not code signed.", "type": "boolean"}, "generateUpdatesFilesForAllChannels": {"default": false, "description": "Please see [Building and Releasing using Channels](https://github.com/electron-userland/electron-builder/issues/1182#issuecomment-324947139).", "type": "boolean"}, "icon": {"default": "build/icon.ico", "description": "The path to application icon.", "type": ["null", "string"]}, "legalTrademarks": {"description": "The trademarks and registered trademarks.", "type": ["null", "string"]}, "protocols": {"anyOf": [{"$ref": "#/definitions/Protocol"}, {"items": {"$ref": "#/definitions/Protocol"}, "type": "array"}], "description": "The URL protocol schemes."}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}], "description": "Publisher configuration. See [Auto Update](./publish.md) for more information."}, "releaseInfo": {"$ref": "#/definitions/ReleaseInfo", "description": "The release info. Intended for command line usage:\n\n```\n-c.releaseInfo.releaseNotes=\"new features\"\n```"}, "requestedExecutionLevel": {"anyOf": [{"enum": ["asInvoker", "highestAvailable", "requireAdministrator"], "type": "string"}, {"type": "null"}], "default": "asInvoker", "description": "The [security level](https://msdn.microsoft.com/en-us/library/6ad1fshk.aspx#Anchor_9) at which the application requests to be executed.\nCannot be specified per target, allowed only in the `win`."}, "signAndEditExecutable": {"default": true, "description": "Whether to sign and add metadata to executable. Advanced option.", "type": "boolean"}, "signExts": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "Explicit file extensions to also sign. Advanced option."}, "signtoolOptions": {"anyOf": [{"$ref": "#/definitions/WindowsSigntoolConfiguration"}, {"type": "null"}], "description": "Options for usage with signtool.exe\nCannot be used in conjunction with `azureSignOptions`, signing will default to Azure Trusted Signing"}, "target": {"anyOf": [{"$ref": "#/definitions/TargetConfiguration"}, {"items": {"anyOf": [{"$ref": "#/definitions/TargetConfiguration"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "default": "nsis", "description": "The target package type: list of `nsis`, `nsis-web` (Web installer), `portable` ([portable]./nsis.md#portable) app without installation), `appx`, `msi`, `msi-wrapped`, `squirrel`, `7z`, `zip`, `tar.xz`, `tar.lz`, `tar.gz`, `tar.bz2`, `dir`.\nAppX package can be built only on Windows 10.\n\nTo use Squirrel.Windows please install `electron-builder-squirrel-windows` dependency."}, "verifyUpdateCodeSignature": {"default": true, "description": "Whether to verify the signature of an available update before installation.\nThe [publisher name](#publisherName) will be used for the signature verification.", "type": "boolean"}}, "type": "object"}, "WindowsSigntoolConfiguration": {"additionalProperties": false, "properties": {"additionalCertificateFile": {"description": "The path to an additional certificate file you want to add to the signature block.", "type": ["null", "string"]}, "certificateFile": {"description": "The path to the *.pfx certificate you want to sign with. Please use it only if you cannot use env variable `CSC_LINK` (`WIN_CSC_LINK`) for some reason.\nPlease see [Code Signing](./code-signing.md).", "type": ["null", "string"]}, "certificatePassword": {"description": "The password to the certificate provided in `certificateFile`. Please use it only if you cannot use env variable `CSC_KEY_PASSWORD` (`WIN_CSC_KEY_PASSWORD`) for some reason.\nPlease see [Code Signing](./code-signing.md).", "type": ["null", "string"]}, "certificateSha1": {"description": "The SHA1 hash of the signing certificate. The SHA1 hash is commonly specified when multiple certificates satisfy the criteria specified by the remaining switches. Works only on Windows (or on macOS if [Parallels Desktop](https://www.parallels.com/products/desktop/) Windows 10 virtual machines exits).", "type": ["null", "string"]}, "certificateSubjectName": {"description": "The name of the subject of the signing certificate, which is often labeled with the field name `issued to`. Required only for EV Code Signing and works only on Windows (or on macOS if [Parallels Desktop](https://www.parallels.com/products/desktop/) Windows 10 virtual machines exits).", "type": ["null", "string"]}, "publisherName": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "[The publisher name](https://github.com/electron-userland/electron-builder/issues/1187#issuecomment-*********), exactly as in your code signed certificate. Several names can be provided.\nDefaults to common name from your code signing certificate."}, "rfc3161TimeStampServer": {"default": "http://timestamp.digicert.com", "description": "The URL of the RFC 3161 time stamp server.", "type": ["null", "string"]}, "sign": {"anyOf": [{"typeof": "function"}, {"type": ["null", "string"]}], "description": "The custom function (or path to file or module id) to sign Windows executables"}, "signingHashAlgorithms": {"anyOf": [{"items": {"enum": ["sha1", "sha256"], "type": "string"}, "type": "array"}, {"type": "null"}], "default": "['sha1', 'sha256']", "description": "Array of signing algorithms used. For AppX `sha256` is always used."}, "timeStampServer": {"default": "http://timestamp.digicert.com", "description": "The URL of the time stamp server.", "type": ["null", "string"]}}, "type": "object"}}, "properties": {"afterAllArtifactBuild": {"anyOf": [{"typeof": "function"}, {"type": ["null", "string"]}], "description": "The function (or path to file or module id) to be run after all artifacts are built.\n\n```typescript\n(buildResult: BuildResult): Promise<Array<string>> | Array<string>\n```\n\nConfiguration in the same way as `afterPack` (see above).\n\n!!! example \"myAfterAllArtifactBuild.js\"\n```js\nexports.default = function () {\n // you can return additional files to publish\n return [\"/path/to/additional/result/file\"]\n}\n```"}, "afterExtract": {"anyOf": [{"typeof": "function"}, {"type": ["null", "string"]}], "description": "The function (or path to file or module id) to be [run after the prebuilt Electron binary has been extracted to the output directory](#afterextract)\nSame setup as {@link beforePack}"}, "afterPack": {"anyOf": [{"typeof": "function"}, {"type": ["null", "string"]}], "description": "The function (or path to file or module id) to be [run after pack](#afterpack) (but before pack into distributable format and sign).\nSame setup as {@link beforePack}"}, "afterSign": {"anyOf": [{"typeof": "function"}, {"type": ["null", "string"]}], "description": "The function (or path to file or module id) to be [run after pack and sign](#aftersign) (but before pack into distributable format).\nSame setup as {@link beforePack}"}, "apk": {"anyOf": [{"$ref": "#/definitions/LinuxTargetSpecificOptions"}, {"type": "null"}]}, "appId": {"default": "com.electron.${name}", "description": "The application id. Used as [CFBundleIdentifier](https://developer.apple.com/library/ios/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html#//apple_ref/doc/uid/********-102070) for MacOS and as\n[Application User Model ID](https://msdn.microsoft.com/en-us/library/windows/desktop/dd378459(v=vs.85).aspx) for Windows (NSIS target only, Squirrel.Windows not supported). It is strongly recommended that an explicit ID is set.", "type": ["null", "string"]}, "appImage": {"anyOf": [{"$ref": "#/definitions/AppImageOptions"}, {"type": "null"}], "description": "AppImage options."}, "appx": {"anyOf": [{"$ref": "#/definitions/AppXOptions"}, {"type": "null"}]}, "appxManifestCreated": {"anyOf": [{"typeof": "function"}, {"type": ["null", "string"]}], "description": "The function (or path to file or module id) to be run after Appx manifest created on disk - not packed into .appx package yet."}, "artifactBuildCompleted": {"anyOf": [{"typeof": "function"}, {"type": ["null", "string"]}], "description": "The function (or path to file or module id) to be run on artifact build completed.\nSame setup as {@link beforePack}"}, "artifactBuildStarted": {"anyOf": [{"typeof": "function"}, {"type": ["null", "string"]}], "description": "The function (or path to file or module id) to be run on artifact build start.\nSame setup as {@link beforePack}"}, "artifactName": {"description": "The [artifact file name template](./configuration.md#artifact-file-name-template). Defaults to `${productName}-${version}.${ext}` (some target can have other defaults, see corresponding options).", "type": ["null", "string"]}, "asar": {"anyOf": [{"$ref": "#/definitions/AsarOptions"}, {"type": ["null", "boolean"]}], "default": true, "description": "Whether to package the application's source code into an archive, using [Electron's archive format](http://electron.atom.io/docs/tutorial/application-packaging/).\n\nNode modules, that must be unpacked, will be detected automatically, you don't need to explicitly set [asarUnpack](#asarUnpack) - please file an issue if this doesn't work."}, "asarUnpack": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "A [glob patterns](./file-patterns.md) relative to the [app directory](#directories), which specifies which files to unpack when creating the [asar](http://electron.atom.io/docs/tutorial/application-packaging/) archive."}, "beforeBuild": {"anyOf": [{"typeof": "function"}, {"type": ["null", "string"]}], "description": "The function (or path to file or module id) to be run before dependencies are installed or rebuilt. Works when `npmRebuild` is set to `true`. Resolving to `false` will skip dependencies install or rebuild.\n\nIf provided and `node_modules` are missing, it will not invoke production dependencies check."}, "beforePack": {"anyOf": [{"typeof": "function"}, {"type": ["null", "string"]}], "description": "The function (or path to file or module id) to be run before pack.\n\n```typescript\n(context: BeforePackContext): Promise<any> | any\n```\n\n!!! example \"As function\"\n\n ```js\n beforePack: async (context) => {\n   // your code\n }\n ```\n\nBecause in a configuration file you cannot use JavaScript, can be specified as a path to file or module id. Function must be exported as default export.\n\n```json\n\"build\": {\n\"beforePack\": \"./myBeforePackHook.js\"\n}\n```\n\nFile `myBeforePackHook.js` in the project root directory:\n\n!!! example \"myBeforePackHook.js\"\n ```js\n exports.default = async function(context) {\n   // your custom code\n }\n ```"}, "buildDependenciesFromSource": {"default": false, "description": "Whether to build the application native dependencies from source.", "type": "boolean"}, "buildNumber": {"description": "The build number. Maps to the `--iteration` flag for builds using FPM on Linux.\nIf not defined, then it will fallback to `BUILD_NUMBER` or `TRAVIS_BUILD_NUMBER` or `APPVEYOR_BUILD_NUMBER` or `CIRCLE_BUILD_NUM` or `BUILD_BUILDNUMBER` or `CI_PIPELINE_IID` env.", "type": ["null", "string"]}, "buildVersion": {"description": "The build version. Maps to the `CFBundleVersion` on macOS, and `FileVersion` metadata property on Windows. Defaults to the `version`.\nIf `buildVersion` is not defined and `buildNumber` (or one of the `buildNumber` envs) is defined, it will be used as a build version (`version.buildNumber`).", "type": ["null", "string"]}, "compression": {"anyOf": [{"enum": ["maximum", "normal", "store"], "type": "string"}, {"type": "null"}], "default": "normal", "description": "The compression level. If you want to rapidly test build, `store` can reduce build time significantly. `maximum` doesn't lead to noticeable size difference, but increase build time."}, "copyright": {"default": "Copyright © year ${author}", "description": "The human-readable copyright line for the app.", "type": ["null", "string"]}, "cscKeyPassword": {"type": ["null", "string"]}, "cscLink": {"type": ["null", "string"]}, "deb": {"anyOf": [{"$ref": "#/definitions/DebOptions"}, {"type": "null"}], "description": "Debian package options."}, "defaultArch": {"type": "string"}, "detectUpdateChannel": {"default": true, "description": "Whether to infer update channel from application version pre-release components. e.g. if version `0.12.1-alpha.1`, channel will be set to `alpha`. Otherwise to `latest`.\nThis does *not* apply to github publishing, which will [never auto-detect the update channel](https://github.com/electron-userland/electron-builder/issues/8589).", "type": "boolean"}, "directories": {"anyOf": [{"$ref": "#/definitions/MetadataDirectories"}, {"type": "null"}], "description": "Directories for build resources"}, "disableDefaultIgnoredFiles": {"default": false, "description": "Whether to exclude all default ignored files(https://www.electron.build/contents#files) and options. Defaults to `false`.", "type": ["null", "boolean"]}, "disableSanityCheckAsar": {"default": false, "description": "Whether to disable sanity check asar package (useful for custom electron forks that implement their own encrypted integrity validation)", "type": "boolean"}, "dmg": {"anyOf": [{"$ref": "#/definitions/DmgOptions"}, {"type": "null"}], "description": "macOS DMG options."}, "downloadAlternateFFmpeg": {"description": "Whether to download the alternate FFmpeg library from Electron's release assets and replace the default FFmpeg library prior to signing", "type": "boolean"}, "electronBranding": {"$ref": "#/definitions/ElectronBrandingOptions", "description": "The branding used by Electron's distributables. This is needed if a fork has modified Electron's BRANDING.json file."}, "electronCompile": {"description": "Whether to use [electron-compile](http://github.com/electron/electron-compile) to compile app. Defaults to `true` if `electron-compile` in the dependencies. And `false` if in the `devDependencies` or doesn't specified.", "type": "boolean"}, "electronDist": {"anyOf": [{"typeof": "function"}, {"type": ["null", "string"]}], "description": "The function (or path to file or module id) to be run when staging the electron artifact environment.\nReturns the path to custom Electron build (e.g. `~/electron/out/R`) or folder of electron zips.\n\nZip files must follow the pattern `electron-v${version}-${platformName}-${arch}.zip`, otherwise it will be assumed to be an unpacked Electron app directory"}, "electronDownload": {"$ref": "#/definitions/ElectronDownloadOptions", "description": "The [electron-download](https://github.com/electron-userland/electron-download#usage) options."}, "electronFuses": {"anyOf": [{"$ref": "#/definitions/FuseOptionsV1"}, {"type": "null"}], "description": "Options to pass to `@electron/fuses`\nRef: https://github.com/electron/fuses"}, "electronLanguages": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}], "description": "The electron locales to keep. By default, all Electron locales used as-is."}, "electronUpdaterCompatibility": {"description": "The [electron-updater compatibility](./auto-update.md#compatibility) semver range.", "type": ["null", "string"]}, "electronVersion": {"description": "The version of electron you are packaging for. Defaults to version of `electron`, `electron-prebuilt` or `electron-prebuilt-compile` dependency.", "type": ["null", "string"]}, "executableName": {"description": "The executable name. Defaults to `productName`.", "type": ["null", "string"]}, "extends": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "The name of a built-in configuration preset (currently, only `react-cra` is supported) or any number of paths to config files (relative to project dir).\n\nThe latter allows to mixin a config from multiple other configs, as if you `Object.assign` them, but properly combine `files` glob patterns.\n\nIf `react-scripts` in the app dependencies, `react-cra` will be set automatically. Set to `null` to disable automatic detection."}, "extraFiles": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"items": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "description": "The same as [extraResources](#extraresources) but copy into the app's content directory (`Contents` for MacOS, root directory for Linux and Windows)."}, "extraMetadata": {"description": "Inject properties to `package.json`."}, "extraResources": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"items": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "description": "A [glob patterns](./file-patterns.md) relative to the project directory, when specified, copy the file or directory with matching names directly into the app's resources directory (`Contents/Resources` for MacOS, `resources` for Linux and Windows).\n\nFile patterns (and support for `from` and `to` fields) the same as for [files](#files)."}, "fileAssociations": {"anyOf": [{"$ref": "#/definitions/FileAssociation"}, {"items": {"$ref": "#/definitions/FileAssociation"}, "type": "array"}], "description": "The file associations."}, "files": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"items": {"anyOf": [{"$ref": "#/definitions/FileSet"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}], "description": "A [glob patterns](./file-patterns.md) relative to the [app directory](configuration.md#directories), which specifies which files to include when copying files to create the package.\n\nDefaults to:\n```json\n[\n\"**\\/*\",\n\"!**\\/node_modules/*\\/{CHANGELOG.md,README.md,README,readme.md,readme}\",\n\"!**\\/node_modules/*\\/{test,__tests__,tests,powered-test,example,examples}\",\n\"!**\\/node_modules/*.d.ts\",\n\"!**\\/node_modules/.bin\",\n\"!**\\/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}\",\n\"!.editorconfig\",\n\"!**\\/._*\",\n\"!**\\/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}\",\n\"!**\\/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}\",\n\"!**\\/{appveyor.yml,.travis.yml,circle.yml}\",\n\"!**\\/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}\"\n]\n```\n\nDevelopment dependencies are never copied in any case. You don't need to ignore it explicitly. Hidden files are not ignored by default, but all files that should be ignored, are ignored by default.\n\nDefault pattern \\`**\\/*\\` **is not added to your custom** if some of your patterns is not ignore (i.e. not starts with `!`). `package.json` and \\`**\\/node_modules/**\\/*` (only production dependencies will be copied) is added to your custom in any case. All default ignores are added in any case — you don't need to repeat it if you configure own patterns.\n\nMay be specified in the platform options (e.g. in the [mac](mac.md)).\n\nYou may also specify custom source and destination directories by using `FileSet` objects instead of simple glob patterns.\n\n```json\n[\n{\n\"from\": \"path/to/source\",\n\"to\": \"path/to/destination\",\n\"filter\": [\"**\\/*\", \"!foo/*.js\"]\n}\n]\n```\n\nYou can use [file macros](./file-patterns.md#file-macros) in the `from` and `to` fields as well. `from` and `to` can be files and you can use this to [rename](https://github.com/electron-userland/electron-builder/issues/1119) a file while packaging."}, "flatpak": {"anyOf": [{"$ref": "#/definitions/FlatpakOptions"}, {"type": "null"}], "description": "Flatpak options."}, "forceCodeSigning": {"default": false, "description": "Whether to fail if the application is not signed (to prevent unsigned app if code signing configuration is not correct).", "type": "boolean"}, "framework": {"description": "The framework name. One of `electron`, `proton`, `libui`. Defaults to `electron`.", "type": ["null", "string"]}, "freebsd": {"anyOf": [{"$ref": "#/definitions/LinuxTargetSpecificOptions"}, {"type": "null"}]}, "generateUpdatesFilesForAllChannels": {"default": false, "description": "Please see [Building and Releasing using Channels](https://github.com/electron-userland/electron-builder/issues/1182#issuecomment-324947139).", "type": "boolean"}, "icon": {"type": ["null", "string"]}, "includePdb": {"default": false, "description": "Whether to include PDB files.", "type": "boolean"}, "launchUiVersion": {"description": "*libui-based frameworks only* The version of LaunchUI you are packaging for. Applicable for Windows only. Defaults to version suitable for used framework version.", "type": ["null", "string", "boolean"]}, "linux": {"anyOf": [{"$ref": "#/definitions/LinuxConfiguration"}, {"type": "null"}], "description": "Options related to how build Linux targets."}, "mac": {"anyOf": [{"$ref": "#/definitions/MacConfiguration"}, {"type": "null"}], "description": "Options related to how build macOS targets."}, "mas": {"anyOf": [{"$ref": "#/definitions/MasConfiguration"}, {"type": "null"}], "description": "MAS (Mac Application Store) options."}, "masDev": {"anyOf": [{"$ref": "#/definitions/MasConfiguration"}, {"type": "null"}], "description": "MAS (Mac Application Store) development options (`mas-dev` target)."}, "msi": {"anyOf": [{"$ref": "#/definitions/MsiOptions"}, {"type": "null"}]}, "msiProjectCreated": {"anyOf": [{"typeof": "function"}, {"type": ["null", "string"]}], "description": "The function (or path to file or module id) to be run after MSI project created on disk - not packed into .msi package yet."}, "msiWrapped": {"anyOf": [{"$ref": "#/definitions/MsiWrappedOptions"}, {"type": "null"}]}, "nativeRebuilder": {"anyOf": [{"enum": ["legacy", "parallel", "sequential"], "type": "string"}, {"type": "null"}], "default": "sequential", "description": "Use `legacy` app-builder binary for installing native dependencies, or `@electron/rebuild` in `sequential` or `parallel` compilation modes."}, "nodeGypRebuild": {"default": false, "description": "Whether to execute `node-gyp rebuild` before starting to package the app.\n\nDon't [use](https://github.com/electron-userland/electron-builder/issues/683#issuecomment-241214075) [npm](http://electron.atom.io/docs/tutorial/using-native-node-modules/#using-npm) (neither `.npmrc`) for configuring electron headers. Use `electron-builder node-gyp-rebuild` instead.", "type": "boolean"}, "nodeVersion": {"description": "*libui-based frameworks only* The version of NodeJS you are packaging for.\nYou can set it to `current` to set the Node.js version that you use to run.", "type": ["null", "string"]}, "npmArgs": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": ["null", "string"]}], "description": "Additional command line arguments to use when installing app native deps."}, "npmRebuild": {"default": true, "description": "Whether to [rebuild](https://docs.npmjs.com/cli/rebuild) native dependencies before starting to package the app.", "type": "boolean"}, "nsis": {"anyOf": [{"$ref": "#/definitions/NsisOptions"}, {"type": "null"}]}, "nsisWeb": {"anyOf": [{"$ref": "#/definitions/NsisWebOptions"}, {"type": "null"}]}, "onNodeModuleFile": {"anyOf": [{"typeof": "function"}, {"type": ["null", "string"]}], "description": "The function (or path to file or module id) to be [run on each node module](#onnodemodulefile) file. Returning `true`/`false` will determine whether to force include or to use the default copier logic"}, "p5p": {"anyOf": [{"$ref": "#/definitions/LinuxTargetSpecificOptions"}, {"type": "null"}]}, "pacman": {"anyOf": [{"$ref": "#/definitions/LinuxTargetSpecificOptions"}, {"type": "null"}]}, "pkg": {"anyOf": [{"$ref": "#/definitions/PkgOptions"}, {"type": "null"}], "description": "macOS PKG options."}, "portable": {"anyOf": [{"$ref": "#/definitions/PortableOptions"}, {"type": "null"}]}, "productName": {"description": "As [name](#metadata), but allows you to specify a product name for your executable which contains spaces and other special characters not allowed in the [name property](https://docs.npmjs.com/files/package.json#name).\nIf not specified inside of the `build` configuration, `productName` property defined at the top level of `package.json` is used. If not specified at the top level of `package.json`, [name property](https://docs.npmjs.com/files/package.json#name) is used.", "type": ["null", "string"]}, "protocols": {"anyOf": [{"$ref": "#/definitions/Protocol"}, {"items": {"$ref": "#/definitions/Protocol"}, "type": "array"}], "description": "The URL protocol schemes."}, "publish": {"anyOf": [{"$ref": "#/definitions/GithubOptions"}, {"$ref": "#/definitions/S3Options"}, {"$ref": "#/definitions/SpacesOptions"}, {"$ref": "#/definitions/GenericServerOptions"}, {"$ref": "#/definitions/CustomPublishOptions"}, {"$ref": "#/definitions/KeygenOptions"}, {"$ref": "#/definitions/SnapStoreOptions"}, {"$ref": "#/definitions/BitbucketOptions"}, {"items": {"$ref": "#/definitions/AllPublishOptions"}, "type": "array"}, {"type": ["null", "string"]}], "description": "Publisher configuration. See [Auto Update](./publish.md) for more information."}, "releaseInfo": {"$ref": "#/definitions/ReleaseInfo", "description": "The release info. Intended for command line usage:\n\n```\n-c.releaseInfo.releaseNotes=\"new features\"\n```"}, "removePackageKeywords": {"default": true, "description": "Whether to remove `keywords` field from `package.json` files.", "type": "boolean"}, "removePackageScripts": {"default": true, "description": "Whether to remove `scripts` field from `package.json` files.", "type": "boolean"}, "rpm": {"anyOf": [{"$ref": "#/definitions/LinuxTargetSpecificOptions"}, {"type": "null"}]}, "snap": {"anyOf": [{"$ref": "#/definitions/SnapOptions"}, {"type": "null"}], "description": "Snap options."}, "squirrelWindows": {"anyOf": [{"$ref": "#/definitions/SquirrelWindowsOptions"}, {"type": "null"}]}, "target": {"anyOf": [{"$ref": "#/definitions/TargetConfiguration"}, {"items": {"anyOf": [{"$ref": "#/definitions/TargetConfiguration"}, {"type": "string"}]}, "type": "array"}, {"type": ["null", "string"]}]}, "win": {"anyOf": [{"$ref": "#/definitions/WindowsConfiguration"}, {"type": "null"}], "description": "Options related to how build Windows targets."}, "$schema": {"description": "JSON Schema for this document.", "type": ["null", "string"]}}, "type": "object"}