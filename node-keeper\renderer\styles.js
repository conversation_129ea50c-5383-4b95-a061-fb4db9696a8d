html, body {
  margin: 0;
  background: transparent;
  font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, <PERSON><PERSON>, <PERSON><PERSON>, "Apple Color Emoji", "Segoe UI Emoji";
}

.card {
  width: 320px;
  height: 140px;
  padding: 10px 12px;
  box-sizing: border-box;
  border-radius: 12px;
  background: #0b1220;
  color: #e8f0ff;
  border: 1px solid #1c2740;
  box-shadow: 0 10px 40px rgba(0,0,0,.35);
  -webkit-app-region: drag; /* allows dragging the frameless window */
}

.row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title {
  font-weight: 700;
  font-size: 16px;
  letter-spacing: .2px;
}

.info {
  font-size: 14px;
  margin-top: 4px;
  opacity: 0.95;
}

.small {
  opacity: 0.75;
  font-size: 12px;
}

.actions {
  margin-top: 8px;
  gap: 6px;
}

.actions button {
  -webkit-app-region: no-drag; /* buttons must be clickable */
  border: 1px solid #344674;
  background: #152038;
  color: #cfe0ff;
  border-radius: 8px;
  padding: 6px 10px;
  font-size: 13px;
  cursor: pointer;
  min-height: 28px;
}
.actions button:hover {
  background: #1c2a4a;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: gray;
  box-shadow: 0 0 8px rgba(255,255,255,.15);
}

.drag { -webkit-app-region: drag; }

/* Optimizations for smaller screens */
@media (max-width: 400px) {
  .card {
    width: 100%;
    max-width: 380px;
    height: auto;
    min-height: 160px;
    padding: 15px;
  }

  .title {
    font-size: 18px;
  }

  .info {
    font-size: 16px;
    margin-top: 6px;
  }

  .small {
    font-size: 14px;
  }

  .actions {
    margin-top: 12px;
    gap: 8px;
    flex-wrap: wrap;
  }

  .actions button {
    padding: 8px 12px;
    font-size: 14px;
    min-height: 36px;
    flex: 1;
    min-width: 70px;
  }

  .dot {
    width: 14px;
    height: 14px;
  }
}
