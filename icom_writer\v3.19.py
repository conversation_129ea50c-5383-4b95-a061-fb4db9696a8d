﻿# CS-F7500 UIA automation - v3.19
# - Strict COM-port navigation (Alt+F, Right x2 to "Com Port", Down, Enter -> popup)
# - Popup: pick the COMx that matches radio_config
# - Abides by DEV_OPTION_STRICT_NAV for the entire COM feature
# - Kills EX3852A.EXE before start
# - Absolute-from-Home with HARD HOME, F2 for Name, Mode via dropdown (Enter, Down, Enter)
# - Detached launch + PID/title attach
# - ASCII-only to avoid encoding issues

import os
import time
import csv
import subprocess
from collections import defaultdict
from datetime import datetime
from pywinauto import Desktop
from pywinauto.application import Application
from pywinauto import keyboard

# =========================
# CONFIG / PATHS
# =========================
WORKDIR = r"C:\cc\icom"
EXE_PATH = r"C:\Program Files (x86)\Icom\CS-F7500\EX3852A.EXE"

os.makedirs(WORKDIR, exist_ok=True)
CSV_FILE = os.path.join(WORKDIR, "new_channel.csv")
RADIO_CONFIG_FILE = os.path.join(WORKDIR, "radio_config.csv")
LOG_FILE = os.path.join(WORKDIR, "programming_log.txt")

WINDOW_TITLE_KEYS = ["CS-F7500", "Icom", "EX3852A"]

# Timeouts / waits (seconds)
POST_LAUNCH_WAIT   = 1.5
ATTACH_TIMEOUT     = 60.0
POST_ATTACH_SETTLE = 1.0
POST_MAXIMIZE_WAIT = 1.0
TREE_RETRY_SECONDS = 25
COM_DIALOG_WAIT    = 15.0

# Click inside the grid area to wake focus (adjust if needed)
GRID_NUDGE_POINT = (900, 400)

# Absolute offsets: RIGHT from Home to each column (your working hops)
HOME_TO_COL = {
    "name":  4,
    "mode":  5,
    "rx":    6,   # RX is required
    "tx":    7,
    "rxctc": 9,
    "txctc": 10,
}

# Mode dropdown: Enter, DOWN N, Enter (Analog = 1 down)
MODE_DROPDOWN_DOWN_PRESSES = 1

# COM selection behavior toggle (dev option): when True, use the exact strict sequence you specified.
DEV_OPTION_STRICT_NAV = True

ALWAYS_SET_TONE_MODE = False
PAUSE_ON_ERROR = True
LOG_TO_FILE = True

# =========================
# LOGGING
# =========================
def log(msg: str):
    ts = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
    line = f"{ts} {msg}"
    print(line)
    if LOG_TO_FILE:
        try:
            with open(LOG_FILE, "a", encoding="utf-8") as f:
                f.write(line + "\n")
        except Exception:
            pass

def pause(label=""):
    if PAUSE_ON_ERROR:
        try:
            input(f"[{label}] Press Enter to continue...")
        except Exception:
            time.sleep(2)

# =========================
# CSV LOADING (enc fallback)
# =========================
def load_csv_with_fallbacks(path):
    encodings = ["utf-8-sig", "utf-8", "cp1252", "latin-1"]
    last_err = None
    for enc in encodings:
        try:
            with open(path, newline="", encoding=enc) as f:
                return list(csv.DictReader(f)), enc
        except Exception as e:
            last_err = e
    raise last_err

def read_radio_config(path):
    # returns dict: radio_id(lower) -> "COMx"
    def normalize_com(s):
        s = (s or "").strip().upper()
        if not s:
            return ""
        if s.startswith("COM"):
            return s
        if s.isdigit():
            return "COM" + s
        return s.replace(" ", "").replace("-", "")
    try:
        dict_rows, used = load_csv_with_fallbacks(path)
        log(f"Loaded radio_config with encoding: {used}")
    except Exception as e:
        log(f"ERROR reading radio_config: {e}")
        return {}
    out = {}
    for r in dict_rows:
        rdradio = (r.get("radio") or r.get("unit") or r.get("device") or r.get("radio_id") or "").strip()
        com = (r.get("comport") or r.get("com_port") or r.get("com") or r.get("port") or "").strip()
        if rdradio:
            out[rdradio.lower()] = normalize_com(com)
    return out

def parse_new_channels(dict_rows):
    # returns list of dicts with keys: radio,name,rx,tx,rxtone,txtone
    out = []
    def get(r, *names):
        for n in names:
            v = r.get(n)
            if v is not None:
                return v.strip()
        return ""
    for r in dict_rows:
        radio  = get(r, "radio", "unit", "device", "radio_id")
        name   = get(r, "name")
        rx     = get(r, "rx")
        tx     = get(r, "tx")
        rxtone = get(r, "rxtone", "rx_ctone")
        txtone = get(r, "txtone", "tx_ctone")
        out.append({
            "radio": radio,
            "name": name,
            "rx": rx,
            "tx": tx,
            "rxtone": rxtone,
            "txtone": txtone,
        })
    return out

# =========================
# PRELAUNCH CLEANUP
# =========================
def kill_existing_processes():
    names = ["EX3852A.EXE"]
    for name in names:
        try:
            r = subprocess.run(
                ["taskkill", "/F", "/IM", name],
                capture_output=True, text=True, shell=False
            )
            out = (r.stdout or "") + (r.stderr or "")
            if r.returncode == 0:
                log(f"Killed existing {name}")
            else:
                low = out.lower()
                if "no instance" in low or "not found" in low:
                    log(f"No running {name} to kill.")
                else:
                    log(f"WARN taskkill {name}: rc={r.returncode} msg='{out.strip()[:200]}'")
        except Exception as e:
            log(f"WARN taskkill {name}: {e}")
    time.sleep(0.8)

# =========================
# WINDOW DISCOVERY / ATTACH
# =========================
def list_top_windows():
    try:
        wins = Desktop(backend="uia").windows()
        for w in wins:
            try:
                log(f"WIN pid={w.process_id} vis={w.is_visible()} title='{w.window_text()}' class='{w.friendly_class_name()}'")
            except Exception:
                pass
    except Exception:
        pass

def try_connect_by_pid(pid):
    try:
        app = Application(backend="uia").connect(process=pid, timeout=5)
        win = app.top_window()
        return app, win
    except Exception:
        return None, None

def find_window_by_title_keys():
    wins = Desktop(backend="uia").windows()
    for w in wins:
        try:
            title = (w.window_text() or "").strip()
            if not title:
                continue
            for key in WINDOW_TITLE_KEYS:
                if key.lower() in title.lower() and w.is_visible():
                    return w
        except Exception:
            continue
    return None

def has_tree_control(win):
    try:
        t = win.child_window(control_type="Tree")
        return bool(t and t.exists())
    except Exception:
        return False

def attach_or_launch():
    log("Launching detached...")
    try:
        p = subprocess.Popen([EXE_PATH], shell=False)
        launch_pid = p.pid
        log(f"Launched PID={launch_pid}")
    except Exception as e:
        raise RuntimeError(f"launch failed: {e}")

    time.sleep(POST_LAUNCH_WAIT)

    t0 = time.time()
    app = None
    main = None
    last_dump = 0
    while time.time() - t0 < ATTACH_TIMEOUT:
        if app is None:
            app, win = try_connect_by_pid(launch_pid)
            if app and win:
                main = win
                log("Attached by PID to top_window()")

        if main is None:
            w = find_window_by_title_keys()
            if w:
                try:
                    app = Application(backend="uia").connect(handle=w.handle)
                    main = app.window(handle=w.handle)
                    log(f"Attached by title to '{main.window_text()}' (pid={w.process_id})")
                except Exception:
                    main = None

        if main:
            try:
                main.wait("visible", timeout=5)
                break
            except Exception:
                main = None

        if time.time() - last_dump > 5:
            log("Waiting for main window... listing top windows:")
            list_top_windows()
            last_dump = time.time()
        time.sleep(0.4)

    if not main:
        raise RuntimeError("attach_or_launch: could not find a running main window")

    try:
        main.maximize()
        time.sleep(POST_MAXIMIZE_WAIT)
    except Exception as e:
        log(f"WARN maximize: {e}")

    time.sleep(POST_ATTACH_SETTLE)
    return app, main

# =========================
# TREE SELECT
# =========================
def _expand(item):
    try:
        item.expand()
    except Exception:
        try:
            item.double_click_input()
        except Exception:
            pass
    time.sleep(0.15)

def _find_tree_root(main):
    try:
        t = main.child_window(control_type="Tree")
        if t.exists():
            return t
    except Exception:
        pass
    for el in main.descendants():
        try:
            if getattr(el.element_info, "class_name", "") == "TreeView":
                return el
        except Exception:
            continue
    return None

def _find_treeitem(root, label):
    want = label.strip().lower()
    for it in root.descendants(control_type="TreeItem"):
        nm = (it.window_text() or "").strip().lower()
        if nm == want:
            return it
    return None

def select_tree_items(main):
    deadline = time.time() + TREE_RETRY_SECONDS
    MEM, Z1A, Z1B = "Memory CH", "1: Zone 1", "Zone 1"
    while time.time() < deadline:
        root = _find_tree_root(main)
        if not root:
            keyboard.send_keys("{F6}")
            time.sleep(0.5)
            continue

        mem = _find_treeitem(root, MEM)
        if not mem:
            for ti in root.descendants(control_type="TreeItem"):
                _expand(ti)
            mem = _find_treeitem(root, MEM)
        if not mem:
            time.sleep(0.5)
            continue

        try:
            mem.click_input()
        except Exception:
            pass
        _expand(mem)

        zone = _find_treeitem(root, Z1A) or _find_treeitem(root, Z1B)
        if zone:
            try:
                zone.click_input()
            except Exception:
                try:
                    zone.double_click_input()
                except Exception:
                    pass
            log("Selected Memory CH -> Zone 1")
            return
        time.sleep(0.3)
    raise RuntimeError("Could not select 'Memory CH' -> 'Zone 1'")

# =========================
# GRID NAV HELPERS (ABSOLUTE + HARD HOME)
# =========================
def nudge_grid(main):
    try:
        main.click_input(coords=GRID_NUDGE_POINT)
        log("Nudged grid focus.")
    except Exception:
        pass
    time.sleep(0.25)

def ensure_nav_mode():
    keyboard.send_keys("{ESC}")
    time.sleep(0.06)

def hard_home_to_col0():
    ensure_nav_mode()
    keyboard.send_keys("{HOME}")
    time.sleep(0.08)
    keyboard.send_keys("{HOME}")
    time.sleep(0.08)
    for _ in range(8):
        keyboard.send_keys("^({LEFT})")
        time.sleep(0.02)

def hop_from_home(rights: int):
    hard_home_to_col0()
    n = max(0, int(rights or 0))
    if n:
        keyboard.send_keys("{RIGHT %d}" % n)
    time.sleep(0.08)

def goto_col(key: str):
    offs = HOME_TO_COL.get(key)
    if offs is None:
        raise KeyError("Unknown column key: %s" % key)
    hop_from_home(offs)

def enter_value_here(text: str):
    keyboard.send_keys("{ENTER}")
    time.sleep(0.12)
    if text:
        keyboard.send_keys(str(text), with_spaces=True)
    keyboard.send_keys("{ENTER}")
    time.sleep(0.12)
    ensure_nav_mode()

def enter_text_cell(col_key: str, text: str, use_f2: bool = False):
    goto_col(col_key)
    if use_f2:
        keyboard.send_keys("{F2}")
        time.sleep(0.12)
    else:
        keyboard.send_keys("{ENTER}")
        time.sleep(0.12)
    if text:
        keyboard.send_keys(str(text), with_spaces=True)
    keyboard.send_keys("{ENTER}")
    time.sleep(0.12)
    ensure_nav_mode()

def pick_mode_dropdown():
    goto_col("mode")
    keyboard.send_keys("{ENTER}")
    time.sleep(0.12)
    d = max(0, MODE_DROPDOWN_DOWN_PRESSES)
    if d:
        keyboard.send_keys("{DOWN %d}" % d)
        time.sleep(0.08)
    keyboard.send_keys("{ENTER}")
    time.sleep(0.12)
    ensure_nav_mode()

# =========================
# STRICT COM-PORT NAVIGATION (DEV OPTION)
# =========================
def open_com_port_select_popup_strict():
    """
    Strict sequence per your spec:
    1) Alt+F to open 'File'
    2) Right x2 to 'Com Port'
    3) Down x1 to first item 'Select COM Port'
    4) Enter -> popup appears with list of COM ports
    """
    # Step 1: Alt+F
    keyboard.send_keys("%f")
    time.sleep(0.15)
    # Step 2: Right x2 to 'Com Port'
    keyboard.send_keys("{RIGHT 2}")
    time.sleep(0.15)
    # Step 3: Down to 'Select COM Port'
    keyboard.send_keys("{DOWN}")
    time.sleep(0.10)
    # Step 4: Enter to open popup
    keyboard.send_keys("{ENTER}")
    time.sleep(0.25)

    # Wait for popup to appear
    t0 = time.time()
    while time.time() - t0 < COM_DIALOG_WAIT:
        dlg = find_com_select_popup()
        if dlg is not None:
            log("COM Select popup detected (strict path).")
            return dlg
        time.sleep(0.2)
    log("WARN: strict COM Select popup not detected in time.")
    return None

def find_com_select_popup():
    """
    Find the popup window that lists COM ports.
    We search for a dialog with a List/ListItem containing 'COM'.
    """
    wins = Desktop(backend="uia").windows()
    for w in wins:
        try:
            if not w.is_visible():
                continue
            title = (w.window_text() or "").strip()
            # Non-empty title helps, but some popups can be blank — rely on contents
            items = w.descendants()
            saw_com_item = False
            for el in items:
                try:
                    txt = (el.window_text() or "").strip().upper()
                except Exception:
                    continue
                if not txt:
                    continue
                if txt.startswith("COM") or (" COM" in txt):
                    saw_com_item = True
                    break
            if saw_com_item:
                return w
        except Exception:
            continue
    return None

def select_com_in_popup(com_target):
    """
    In the popup: select the COMx item that matches com_target.
    Strategy:
      - Try UIA List/ListItem: exact match on name
      - If not found, type com_target and press Enter (typeahead)
      - If a button 'OK' exists, click it; else Enter closes
    """
    dlg = find_com_select_popup()
    if dlg is None:
        log("ERROR: COM popup not found for selection.")
        return False

    try:
        dlg.set_focus()
    except Exception:
        pass
    time.sleep(0.15)

    # Gather list items
    items = []
    try:
        for el in dlg.descendants():
            try:
                if el.friendly_class_name() in ("ListItem", "ListBoxItem", "TreeItem"):
                    nm = (el.window_text() or "").strip().upper()
                    if nm:
                        items.append((el, nm))
            except Exception:
                continue
    except Exception:
        pass

    # Try exact match
    target = (com_target or "").strip().upper()
    found = False
    for el, nm in items:
        if nm == target:
            try:
                el.click_input()
                time.sleep(0.1)
                # double-click to commit selection
                el.double_click_input()
                found = True
                break
            except Exception:
                pass

    # Fallback: type-ahead + Enter
    if not found and target:
        try:
            keyboard.send_keys(target + "{ENTER}")
            found = True
        except Exception:
            pass

    # Try OK/Select buttons
    closed = False
    try:
        buttons = dlg.descendants(control_type="Button")
        for b in buttons:
            nm = (b.window_text() or "").strip().lower()
            if nm in ("ok", "select", "apply", "close", "set"):
                try:
                    b.click_input()
                    closed = True
                    break
                except Exception:
                    pass
    except Exception:
        pass

    if not closed:
        # Try Enter to close
        try:
            keyboard.send_keys("{ENTER}")
            time.sleep(0.2)
            closed = (find_com_select_popup() is None)
        except Exception:
            pass

    if found:
        log(f"Selected COM '{com_target}' in popup.")
    else:
        log(f"WARN: could not confirm selection of '{com_target}' (may still be applied).")

    if closed:
        log("COM popup closed.")
    else:
        log("WARN: COM popup might still be open.")
    return found

# Optional non-strict fallback if needed (not used when DEV_OPTION_STRICT_NAV=True)
def open_com_port_dialog_fallback():
    # Try some generic menus in case strict path fails
    sequences = [["%c", "p"], ["%t", "o"], ["%o"]]
    for seq in sequences:
        try:
            for key in seq:
                keyboard.send_keys(key); time.sleep(0.2)
            t0 = time.time()
            while time.time() - t0 < COM_DIALOG_WAIT:
                dlg = find_com_select_popup()
                if dlg is not None:
                    log("COM dialog detected (fallback).")
                    return dlg
                time.sleep(0.2)
        except Exception:
            pass
    return None

# =========================
# MAIN
# =========================
def main():
    # Ensure correct working directory
    try:
        os.chdir(WORKDIR)
    except Exception:
        pass

    # Load CSVs first (fail fast)
    try:
        dict_rows, used_enc = load_csv_with_fallbacks(CSV_FILE)
        log(f"Loaded new_channel with encoding: {used_enc}")
    except Exception as e:
        log(f"ERROR reading new_channel: {e}")
        pause("CSV new_channel")
        return
    all_rows = parse_new_channels(dict_rows)
    log(f"Rows: {len(all_rows)}")

    radio_map = read_radio_config(RADIO_CONFIG_FILE)  # radio_id -> COMx
    if not radio_map:
        log("WARN: radio_config missing or empty; COM selection may be skipped if no mapping.")
    else:
        log(f"Radio mappings loaded: {radio_map}")

    # Group rows by radio id (lowercased). Empty radio goes under "" group.
    groups = defaultdict(list)
    for r in all_rows:
        groups[(r.get("radio") or "").lower()].append(r)

    # Kill any pre-existing Icom process
    kill_existing_processes()

    # Launch and attach
    try:
        app, main = attach_or_launch()
    except Exception as e:
        log(f"ERROR attach/launch: {e}")
        log("Desktop windows at failure:")
        list_top_windows()
        pause("Attach")
        return

    # Wait until main UI has a Tree (means it's ready)
    t0 = time.time()
    while time.time() - t0 < 20:
        if has_tree_control(main):
            break
        time.sleep(0.5)

    # Select Memory CH -> Zone 1 once
    try:
        select_tree_items(main)
    except Exception as e:
        log(f"Tree selection failed: {e}")
        pause("Tree")
        return

    # Wake grid before any homing
    nudge_grid(main)

    # Process by radio groups
    for radio_id, rows in groups.items():
        rid_disp = radio_id or "(no radio specified)"
        log(f"=== Processing radio: {rid_disp} ({len(rows)} rows) ===")

        com_target = radio_map.get(radio_id, "")
        if com_target:
            log(f"Setting COM port to {com_target} for radio '{rid_disp}'")

            # STRICT dev-option path
            dlg = None
            if DEV_OPTION_STRICT_NAV:
                dlg = open_com_port_select_popup_strict()
            else:
                dlg = open_com_port_dialog_fallback()

            if dlg is None:
                log("ERROR: Could not open COM Select popup.")
            else:
                select_com_in_popup(com_target)
                # Nudge grid again after dialog closes
                nudge_grid(main)
        else:
            log(f"WARN: No COM mapping found for radio '{rid_disp}'. Skipping COM set.")

        # Now enter all rows for this radio
        for i, r in enumerate(rows, start=1):
            rx = (r.get("rx") or "").strip()
            name = (r.get("name") or "").strip()
            tx = (r.get("tx") or "").strip()
            rxtone = (r.get("rxtone") or "").strip()
            txtone = (r.get("txtone") or "").strip()

            if not rx:
                log(f"Row [{rid_disp}] {i}: SKIP (rx empty)")
                keyboard.send_keys("{DOWN}")
                time.sleep(0.05)
                continue

            log(f"Row [{rid_disp}] {i}: rx={rx} name={name} tx={tx} rxtone={rxtone} txtone={txtone}")

            try:
                # RX (required)
                goto_col("rx");     enter_value_here(rx)
                # Name
                enter_text_cell("name", name, use_f2=True)
                # Mode
                pick_mode_dropdown()
                # TX
                goto_col("tx");     enter_value_here(tx)
                # RX CTC
                goto_col("rxctc")
                if rxtone or ALWAYS_SET_TONE_MODE:
                    keyboard.send_keys("{ENTER}")
                    time.sleep(0.12)
                    keyboard.send_keys("User{ENTER}")
                    time.sleep(0.10)
                    if rxtone:
                        keyboard.send_keys(str(rxtone), with_spaces=True)
                    keyboard.send_keys("{ENTER}")
                    time.sleep(0.12)
                    ensure_nav_mode()
                else:
                    enter_value_here("")
                # TX CTC
                goto_col("txctc")
                if txtone or ALWAYS_SET_TONE_MODE:
                    keyboard.send_keys("{ENTER}")
                    time.sleep(0.12)
                    keyboard.send_keys("User{ENTER}")
                    time.sleep(0.10)
                    if txtone:
                        keyboard.send_keys(str(txtone), with_spaces=True)
                    keyboard.send_keys("{ENTER}")
                    time.sleep(0.12)
                    ensure_nav_mode()
                else:
                    enter_value_here("")
                # Next row
                keyboard.send_keys("{DOWN}")
                time.sleep(0.08)

            except Exception as e:
                log(f"Row [{rid_disp}] {i} ERROR: {e}")
                pause(f"Row {i} ({rid_disp})")
                keyboard.send_keys("{DOWN}")
                time.sleep(0.06)

    log("Done (v3.19).")
    pause("End")

if __name__ == "__main__":
    main()
0000.