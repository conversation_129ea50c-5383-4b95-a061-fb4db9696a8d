{"version": 3, "file": "asyncEventEmitter.js", "sourceRoot": "", "sources": ["../../src/util/asyncEventEmitter.ts"], "names": [], "mappings": ";;;AAAA,+CAAkC;AAClC,+DAAiE;AAoBjE,MAAa,iBAAiB;IAA9B;QACmB,cAAS,GAAuC,IAAI,GAAG,EAAE,CAAA;QACzD,sBAAiB,GAAG,IAAI,wCAAiB,EAAE,CAAA;IAyD9D,CAAC;IAvDC,EAAE,CAAoB,KAAQ,EAAE,QAAwB,EAAE,OAAoB,QAAQ;;QACpF,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAA;QACb,CAAC;QACD,MAAM,SAAS,GAAG,MAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,mCAAI,EAAE,CAAA;QACjD,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAA;QAC3C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;QACpC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,GAAG,CAAoB,KAAQ,EAAE,QAAwB;;QACvD,MAAM,SAAS,GAAG,MAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,0CAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAA;QAChF,IAAI,CAAC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,MAAM,CAAA,EAAE,CAAC;YACvB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;YAC5B,OAAO,IAAI,CAAA;QACb,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;QACpC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,KAAK,CAAC,IAAI,CAAoB,KAAQ,EAAE,GAAG,IAAsB;QAC/D,MAAM,MAAM,GAAG,EAAE,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,CAAA;QAE3D,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;QACtD,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;YAC3B,kBAAG,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,0BAA0B,CAAC,CAAA;YAChD,OAAO,MAAM,CAAA;QACf,CAAC;QAED,MAAM,YAAY,GAAG,KAAK,EAAE,SAAmB,EAAE,EAAE;YACjD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;oBACrC,OAAO,KAAK,CAAA;gBACd,CAAC;gBACD,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;gBACvD,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,GAAG,IAAI,CAAC,CAAC,CAAA;YAC3C,CAAC;YACD,OAAO,IAAI,CAAA;QACb,CAAC,CAAA;QAED,MAAM,CAAC,aAAa,GAAG,MAAM,YAAY,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAA;QAC1F,gCAAgC;QAChC,MAAM,CAAC,WAAW,GAAG,MAAM,YAAY,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAA;QAEtF,OAAO,MAAM,CAAA;IACf,CAAC;IAED,eAAe,CAAoB,KAAQ,EAAE,IAA6B;;QACxE,MAAM,SAAS,GAAG,MAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,mCAAI,EAAE,CAAA;QACjD,OAAO,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;IAClE,CAAC;IAED,KAAK;QACH,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;IACxB,CAAC;CACF;AA3DD,8CA2DC", "sourcesContent": ["import { log } from \"builder-util\"\nimport { CancellationToken, Nullish } from \"builder-util-runtime\"\n\ntype Handler = (...args: any[]) => Promise<void> | void\n\nexport type HandlerType = \"system\" | \"user\"\n\ntype Handle = { handler: Handler; type: HandlerType }\n\nexport type EventMap = {\n  [key: string]: Handler\n}\n\ninterface TypedEventEmitter<Events extends EventMap> {\n  on<E extends keyof Events>(event: E, listener: Events[E] | Nullish, type: HandlerType): this\n  off<E extends keyof Events>(event: E, listener: Events[E] | Nullish): this\n  emit<E extends keyof Events>(event: E, ...args: Parameters<Events[E]>): Promise<{ emittedSystem: boolean; emittedUser: boolean }>\n  filterListeners<E extends keyof Events>(event: E, type: HandlerType): Handle[]\n  clear(): void\n}\n\nexport class AsyncEventEmitter<T extends EventMap> implements TypedEventEmitter<T> {\n  private readonly listeners: Map<keyof T, Handle[] | undefined> = new Map()\n  private readonly cancellationToken = new CancellationToken()\n\n  on<E extends keyof T>(event: E, listener: T[E] | Nullish, type: HandlerType = \"system\"): this {\n    if (!listener) {\n      return this\n    }\n    const listeners = this.listeners.get(event) ?? []\n    listeners.push({ handler: listener, type })\n    this.listeners.set(event, listeners)\n    return this\n  }\n\n  off<E extends keyof T>(event: E, listener: T[E] | Nullish): this {\n    const listeners = this.listeners.get(event)?.filter(l => l.handler !== listener)\n    if (!listeners?.length) {\n      this.listeners.delete(event)\n      return this\n    }\n    this.listeners.set(event, listeners)\n    return this\n  }\n\n  async emit<E extends keyof T>(event: E, ...args: Parameters<T[E]>): Promise<{ emittedSystem: boolean; emittedUser: boolean }> {\n    const result = { emittedSystem: false, emittedUser: false }\n\n    const eventListeners = this.listeners.get(event) || []\n    if (!eventListeners.length) {\n      log.debug({ event }, \"no event listeners found\")\n      return result\n    }\n\n    const emitInternal = async (listeners: Handle[]) => {\n      for (const listener of listeners) {\n        if (this.cancellationToken.cancelled) {\n          return false\n        }\n        const handler = await Promise.resolve(listener.handler)\n        await Promise.resolve(handler?.(...args))\n      }\n      return true\n    }\n\n    result.emittedSystem = await emitInternal(eventListeners.filter(l => l.type === \"system\"))\n    // user handlers are always last\n    result.emittedUser = await emitInternal(eventListeners.filter(l => l.type === \"user\"))\n\n    return result\n  }\n\n  filterListeners<E extends keyof T>(event: E, type: HandlerType | undefined): Handle[] {\n    const listeners = this.listeners.get(event) ?? []\n    return type ? listeners.filter(l => l.type === type) : listeners\n  }\n\n  clear() {\n    this.listeners.clear()\n  }\n}\n"]}