{"name": "bmp-js", "version": "0.1.0", "description": "A pure javascript BMP encoder and decoder", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/shaozilee/bmp-js"}, "keywords": ["bmp", "1bit", "4bit", "8bit", "16bit", "24bit", "32bit", "encoder", "decoder", "image", "javascript", "js"], "author": {"name": "shaozilee", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {}, "devDependencies": {}}