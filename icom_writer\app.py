import os
import base64
import io
import csv
from flask import Flask, request, jsonify, send_from_directory, render_template_string, redirect, url_for, session
from PIL import Image, ImageEnhance, ImageOps
import pytesseract
import configparser

app = Flask(__name__)
app.secret_key = 'icom_secret_key'

# Config
config = configparser.ConfigParser()
config.read('radio_config.cfg')
working_directory = config['DEFAULT'].get('working_directory', './') if 'DEFAULT' in config else './'

# Load tones
tones = ['OFF']
try:
  import json
  with open(os.path.join(working_directory, 'vhf_ctts_tones.json'), 'r') as f:
    tones = json.load(f)
except Exception:
  pass

def get_config():
  config = configparser.ConfigParser()
  config.read(os.path.join(working_directory, 'radio_config.cfg'))
  cfg = {k: v for section in config.sections() for k, v in config[section].items()}
  if 'DEFAULT' in config:
    cfg.update(config['DEFAULT'])
  return cfg

# Password protection
def is_authenticated():
  cfg = get_config()
  if cfg.get('password_protect', 'disabled') == 'enabled':
    return session.get('auth') == cfg.get('main_password', '')
  return True

# Main page
@app.route('/', methods=['GET', 'POST'])
def main():
  cfg = get_config()
  if cfg.get('password_protect', 'disabled') == 'enabled' and not is_authenticated():
    if request.method == 'POST':
      password = request.form.get('auth')
      if password == cfg.get('main_password', ''):
        session['auth'] = password
        return redirect(url_for('main'))
    return render_template_string('''
    <html><head><title>Password Required</title></head>
    <body>
    <h2>Password Protection Enabled</h2>
    <form method="POST">
      <input type="password" name="auth" placeholder="Enter password" required>
      <button type="submit">Access Main Page</button>
    </form>
    </body></html>
    ''')
  # Generate radio sections
  radio_sections = []
  for i in [1,2]:
    enabled = cfg.get(f'radio{i}_enabled', 'disabled') == 'enabled'
    band = cfg.get(f'radio{i}_band', 'vhf')
    comport = cfg.get(f'radio{i}_comport', str(i))
    radio_html = f'''
    <div class="radio-section{' disabled' if not enabled else ''}">
      <h3>RADIO {i} - {band.upper()}</h3>
      <div class="radio-row">
        <div class="form-group">
          <label>Channel Name:</label>
          <input type="text" name="radio{i}Name" maxlength="14">
        </div>
      </div>
      <div class="radio-row">
        <div class="form-group">
          <label>RX Frequency (MHz):</label>
          <input type="number" name="radio{i}ReceiveFreq" step="0.001" min="136.000" max="512.000">
        </div>
        <div class="form-group">
          <label>RX Tone (Hz):</label>
          <select name="radio{i}ReceiveTone">{''.join([f'<option value="{t}">{t}</option>' for t in tones])}</select>
        </div>
      </div>
      <div class="radio-row">
        <div class="form-group">
          <label>TX Frequency (MHz):</label>
          <input type="number" name="radio{i}TransmitFreq" step="0.001" min="136.000" max="512.000">
        </div>
        <div class="form-group">
          <label>TX Tone (Hz):</label>
          <select name="radio{i}TransmitTone">{''.join([f'<option value="{t}">{t}</option>' for t in tones])}</select>
        </div>
      </div>
    </div>'''
    radio_sections.append(radio_html)
  if request.method == 'POST':
    channels = []
    for i in [1,2]:
      rx = request.form.get(f'radio{i}ReceiveFreq')
      tx = request.form.get(f'radio{i}TransmitFreq')
      if rx and tx:
        channels.append({
          'radio': f'radio{i}',
          'name': request.form.get(f'radio{i}Name', f'Channel{i}'),
          'rx': rx,
          'tx': tx,
          'rxtone': request.form.get(f'radio{i}ReceiveTone', 'OFF'),
          'txtone': request.form.get(f'radio{i}TransmitTone', 'OFF')
        })
    csv_path = os.path.join(working_directory, 'new_channel.csv')
    with open(csv_path, 'w', newline='') as csvfile:
      writer = csv.DictWriter(csvfile, fieldnames=['radio','name','rx','tx','rxtone','txtone'])
      writer.writeheader()
      for row in channels:
        writer.writerow(row)
    return render_template_string('<h1>Configuration Applied to Radio!</h1><a href="/">Add Another Configuration</a>')
  return render_template_string(f'''
  <html><head><title>ICOM Writer</title></head><body>
  <h1>ICOM Radio Configuration</h1>
  <form method="POST">
  {''.join(radio_sections)}
  <button type="submit">Save Configuration</button>
  </form>
  </body></html>
  ''')

# Admin panel
@app.route('/admin', methods=['GET', 'POST'])
def admin():
  cfg = get_config()
  if request.method == 'POST':
    # Save config
    for k in request.form:
      cfg[k] = request.form[k]
    with open(os.path.join(working_directory, 'radio_config.cfg'), 'w') as f:
      for k, v in cfg.items():
        f.write(f'{k}={v}\n')
    return redirect(url_for('admin'))
  return render_template_string(f'''
  <html><head><title>Admin Panel</title></head><body>
  <h1>Admin Panel</h1>
  <form method="POST">
  <label>Password Protection: <input type="checkbox" name="password_protect" value="enabled" {'checked' if cfg.get('password_protect','disabled')=='enabled' else ''}></label><br>
  <label>Main Password: <input type="text" name="main_password" value="{cfg.get('main_password','')}"></label><br>
  <label>Radio 1 Enabled: <select name="radio1_enabled"><option value="enabled" {'selected' if cfg.get('radio1_enabled','disabled')=='enabled' else ''}>Enabled</option><option value="disabled" {'selected' if cfg.get('radio1_enabled','disabled')=='disabled' else ''}>Disabled</option></select></label><br>
  <label>Radio 1 Band: <select name="radio1_band"><option value="vhf" {'selected' if cfg.get('radio1_band','vhf')=='vhf' else ''}>VHF</option><option value="uhf" {'selected' if cfg.get('radio1_band','vhf')=='uhf' else ''}>UHF</option></select></label><br>
  <label>Radio 1 COM Port: <input type="number" name="radio1_comport" value="{cfg.get('radio1_comport','1')}"></label><br>
  <label>Radio 2 Enabled: <select name="radio2_enabled"><option value="enabled" {'selected' if cfg.get('radio2_enabled','disabled')=='enabled' else ''}>Enabled</option><option value="disabled" {'selected' if cfg.get('radio2_enabled','disabled')=='disabled' else ''}>Disabled</option></select></label><br>
  <label>Radio 2 Band: <select name="radio2_band"><option value="vhf" {'selected' if cfg.get('radio2_band','vhf')=='vhf' else ''}>VHF</option><option value="uhf" {'selected' if cfg.get('radio2_band','vhf')=='uhf' else ''}>UHF</option></select></label><br>
  <label>Radio 2 COM Port: <input type="number" name="radio2_comport" value="{cfg.get('radio2_comport','2')}"></label><br>
  <button type="submit">Save Config</button>
  </form>
  </body></html>
  ''')

# OCR, CSV, scan page, and static file routes remain unchanged from previous Python version
import os
import base64
import io
import csv
from flask import Flask, request, jsonify, send_from_directory, render_template_string
from PIL import Image, ImageEnhance, ImageOps
import pytesseract
import configparser

app = Flask(__name__)

# Config
config = configparser.ConfigParser()
config.read('radio_config.cfg')
working_directory = config['DEFAULT'].get('working_directory', './') if 'DEFAULT' in config else './'

# OCR endpoint for ICS-205 form
@app.route('/ocr-ics205', methods=['POST'])
def ocr_ics205():
    try:
        image_data = request.json['image']
        base64_data = image_data.split(',')[1] if ',' in image_data else image_data
        image_bytes = base64.b64decode(base64_data)
        image = Image.open(io.BytesIO(image_bytes))
        # Preprocessing: grayscale and contrast
        image = ImageOps.grayscale(image)
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.5)
        text = pytesseract.image_to_string(image)
        # Parse text into array of channel values
        lines = text.split('\n')
        channel_rows = []
        for line in lines:
            if any(char.isdigit() for char in line) and '.' in line:
                parts = line.strip().split()
                name, rx, rxtone, tx, txtone = '', '', '', '', ''
                for i, part in enumerate(parts):
                    if rx == '' and any(c.isdigit() for c in part) and '.' in part:
                        rx = part
                    elif rx and rxtone == '' and (part.replace('.', '').isdigit() or part.lower() in ['off', 'none']):
                        rxtone = part
                    elif rx and rxtone and tx == '' and any(c.isdigit() for c in part) and '.' in part:
                        tx = part
                    elif tx and txtone == '' and (part.replace('.', '').isdigit() or part.lower() in ['off', 'none']):
                        txtone = part
                if len(parts) > 2:
                    name = parts[1]
                channel_rows.append({
                    'name': name,
                    'rx': rx,
                    'rxtone': rxtone,
                    'tx': tx,
                    'txtone': txtone
                })
        return jsonify({'text': text, 'channels': channel_rows})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Write CSV endpoint
@app.route('/write-ics205-csv', methods=['POST'])
def write_ics205_csv():
    channels = request.json.get('channels', [])
    if not channels:
        return jsonify({'message': 'No channel data provided.'}), 400
    csv_path = os.path.join(working_directory, 'new_channel.csv')
    with open(csv_path, 'w', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=['name', 'rx', 'rxtone', 'tx', 'txtone'])
        writer.writeheader()
        for row in channels:
            writer.writerow(row)
    return jsonify({'message': 'Channels written to new_channel.csv successfully.'})

# Scan ICS-205 page (serves HTML/JS)
@app.route('/scan-ics205')
def scan_ics205():
    html = '''
    <!DOCTYPE html>
    <html>
    <head>
      <title>Scan ICS-205 Form</title>
      <style>
        body { font-family: monospace; background: #1a1a1a; color: #00ffff; padding: 40px; text-align: center; }
        #video { width: 100%; max-width: 400px; border: 2px solid #00ffff; border-radius: 8px; }
        #canvas { display: none; }
        button { font-size: 18px; padding: 12px 24px; border-radius: 6px; border: none; background: #0066cc; color: white; margin: 20px; cursor: pointer; }
        button:hover { background: #0088ff; }
      </style>
    </head>
    <body>
      <h1>Scan ICS-205 Form</h1>
      <video id="video" autoplay playsinline style="background:#222;"></video>
      <canvas id="canvas"></canvas>
      <br>
      <button id="capture">Capture & OCR</button>
      <div id="ocr-result"></div>
      <script>
        (function() {
          const video = document.getElementById('video');
          const canvas = document.getElementById('canvas');
          const captureBtn = document.getElementById('capture');
          const ocrResult = document.getElementById('ocr-result');
          function showError(msg) {
            ocrResult.innerHTML = '<div style="color:#f00;">' + msg + '</div>';
          }
          const constraints = { video: { facingMode: { exact: 'environment' } } };
          navigator.mediaDevices.getUserMedia(constraints)
            .then(function(stream) {
              video.srcObject = stream;
            })
            .catch(function(err) {
              navigator.mediaDevices.getUserMedia({ video: true })
                .then(function(stream) {
                  video.srcObject = stream;
                })
                .catch(function(err2) {
                  showError('Camera access failed: ' + err2.message);
                });
            });
          captureBtn.onclick = function() {
            if (!video.srcObject) {
              showError('No camera stream available.');
              return;
            }
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);
            let imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            let data = imageData.data;
            for (let i = 0; i < data.length; i += 4) {
              let avg = (data[i] + data[i+1] + data[i+2]) / 3;
              avg = avg > 128 ? Math.min(255, avg * 1.2) : Math.max(0, avg * 0.8);
              data[i] = data[i+1] = data[i+2] = avg;
            }
            ctx.putImageData(imageData, 0, 0);
            var dataUrl = canvas.toDataURL('image/png');
            ocrResult.innerHTML = 'Processing...';
            fetch('/ocr-ics205', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ image: dataUrl })
            })
            .then(function(res) { return res.json(); })
            .then(function(data) {
              var html = '<h2>OCR Result</h2><pre>' + data.text + '</pre>';
              if (data.channels && data.channels.length) {
                html += '<h2>Parsed Channels</h2><table border="1" style="margin:auto;color:#00ffff;"><tr><th>Name</th><th>RX</th><th>TX</th><th>RX Tone</th><th>TX Tone</th></tr>';
                for (var i = 0; i < data.channels.length; i++) {
                  var row = data.channels[i];
                  html += '<tr><td>' + row.name + '</td><td>' + row.rx + '</td><td>' + row.tx + '</td><td>' + row.rxtone + '</td><td>' + row.txtone + '</td></tr>';
                }
                html += '</table>';
                html += '<button id="confirm">Confirm & Write to CSV</button>';
              }
              ocrResult.innerHTML = html;
              if (data.channels && data.channels.length) {
                document.getElementById('confirm').onclick = function() {
                  fetch('/write-ics205-csv', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ channels: data.channels })
                  })
                  .then(function(res) { return res.json(); })
                  .then(function(resp) {
                    ocrResult.innerHTML += '<div style="color:#0f0;">' + resp.message + '</div>';
                  });
                };
              }
            });
          };
        })();
      </script>
    </body>
    </html>
    '''
    return render_template_string(html)

# Static files
@app.route('/<path:path>')
def static_files(path):
    return send_from_directory('.', path)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=True)
