[paths]
workdir = c:\templee\code\repositories\icom projects\icom_writer
exe_path = C:\Program Files (x86)\Icom\CS-F7500\EX3852A.EXE
new_channel = new_channel.csv
radio_config = radio_config.csv

[nav]
strict_nav = true
mode_down = 1
auto_write_after_group = true
com_before_read = true
com_before_write = true

[grid]
nudge = 900,400
home_to_col = name=4,mode=5,rx=6,tx=7,rxctc=9,txctc=10

[device]
unit_id = helios26
radio1_installed = true
radio1_type = vhf
radio2_installed = false
radio2_type = none

[network]
enabled = true
ssid = Hotlinker
password_b64 = QmlwQm9wQ2l0aU1vPWltJycnJycnJycnJycnJycnJycnJycnJycnJycnJycnJycnJycnJycnQm9wQ2l0eUJlYXRz

[license]
trial = true
phone_masked = trial
phone_hash = 84d9c4b849506b6d8f8075a9000e7e0a254be71060ea889fad3c88395988f4fc
license_key = TRIAL-ICM-KEUGB6F7DRTX2PIRE6MM-00

[service]
enabled = true
service_name = IcomServiceManager
service_manager = service_manager.exe
main_app = icom_writer

[radios]
radio1 = ''''2
