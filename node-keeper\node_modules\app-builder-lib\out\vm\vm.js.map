{"version": 3, "file": "vm.js", "sourceRoot": "", "sources": ["../../src/vm/vm.ts"], "names": [], "mappings": ";;;AAmCA,oCAiBC;AApDD,+CAA0G;AAE1G,uCAA+B;AAC/B,6BAA4B;AAE5B,MAAa,SAAS;IAAtB;QAiBW,sBAAiB,GAAG,IAAI,eAAI,CAAC,GAAG,EAAE;YACzC,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,sBAAsB,CAAC,CAAC;iBACtG,IAAI,CAAC,GAAG,EAAE;gBACT,kBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAA;gBACrC,OAAO,UAAU,CAAA;YACnB,CAAC,CAAC;iBACD,KAAK,CAAC,GAAG,EAAE;gBACV,kBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,yDAAyD,CAAC,CAAA;gBACzE,OAAO,gBAAgB,CAAA;YACzB,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACJ,CAAC;IA3BC,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,GAAG,CAAA;IACjB,CAAC;IAED,IAAI,CAAC,IAAY,EAAE,IAAmB,EAAE,OAAyB,EAAE,eAAe,GAAG,IAAI;QACvF,OAAO,IAAA,mBAAI,EAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,CAAC,CAAA;IACnD,CAAC;IAED,KAAK,CAAC,IAAY,EAAE,IAAmB,EAAE,OAAsB,EAAE,YAAgC;QAC/F,OAAO,IAAA,oBAAK,EAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAA;IACjD,CAAC;IAED,QAAQ,CAAC,IAAY;QACnB,OAAO,IAAI,CAAA;IACb,CAAC;CAaF;AA5BD,8BA4BC;AAEM,KAAK,UAAU,YAAY,CAAC,WAAwB;IACzD,MAAM,iBAAiB,GAAG,2CAAa,eAAe,EAAC,CAAA;IACvD,IAAI,MAAM,GAAkB,EAAE,CAAA;IAC9B,IAAI,CAAC;QACH,MAAM,GAAG,CAAC,MAAM,iBAAiB,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IAChH,CAAC;IAAC,OAAO,MAAM,EAAE,CAAC;QAChB,IAAI,CAAC,MAAM,uBAAe,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YACnE,MAAM,QAAQ,GAAG,2CAAa,UAAU,EAAC,CAAA;YACzC,OAAO,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAA;QACrC,CAAC;IACH,CAAC;IACD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxB,MAAM,IAAI,wCAAyB,CAAC,6HAA6H,CAAC,CAAA;IACpK,CAAC;IAED,iCAAiC;IACjC,OAAO,IAAI,iBAAiB,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,KAAK,SAAS,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,KAAK,WAAW,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;AACxJ,CAAC;AAED,MAAM,eAAe,GAAG,IAAI,eAAI,CAAC,KAAK,IAAI,EAAE;IAC1C,OAAO,IAAA,0BAAkB,EAAC,MAAM,EAAE,CAAC,WAAW,CAAC,CAAC,CAAA;AAClD,CAAC,CAAC,CAAA;AAEW,QAAA,eAAe,GAAG,IAAI,eAAI,CAAC,KAAK,IAAI,EAAE;IACjD,OAAO,IAAA,0BAAkB,EAAC,MAAM,EAAE,CAAC,WAAW,CAAC,CAAC,CAAA;AAClD,CAAC,CAAC,CAAA;AAEK,MAAM,kBAAkB,GAAG,KAAK,EAAE,OAAe,EAAE,IAAc,EAAE,EAAE;IAC1E,IAAI,CAAC;QACH,MAAM,IAAA,mBAAI,EAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QACzB,OAAO,IAAI,CAAA;IACb,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAA;IACd,CAAC;AACH,CAAC,CAAA;AAPY,QAAA,kBAAkB,sBAO9B", "sourcesContent": ["import { DebugLogger, exec, ExtraSpawnOptions, InvalidConfigurationError, log, spawn } from \"builder-util\"\nimport { ExecFileOptions, SpawnOptions } from \"child_process\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { ParallelsVm } from \"./ParallelsVm\"\nexport class VmManager {\n  get pathSep(): string {\n    return path.sep\n  }\n\n  exec(file: string, args: Array<string>, options?: ExecFileOptions, isLogOutIfDebug = true): Promise<string> {\n    return exec(file, args, options, isLogOutIfDebug)\n  }\n\n  spawn(file: string, args: Array<string>, options?: SpawnOptions, extraOptions?: ExtraSpawnOptions): Promise<any> {\n    return spawn(file, args, options, extraOptions)\n  }\n\n  toVmFile(file: string): string {\n    return file\n  }\n\n  readonly powershellCommand = new Lazy(() => {\n    return this.exec(\"powershell.exe\", [\"-NoProfile\", \"-NonInteractive\", \"-Command\", `Get-Command pwsh.exe`])\n      .then(() => {\n        log.info(null, \"identified pwsh.exe\")\n        return \"pwsh.exe\"\n      })\n      .catch(() => {\n        log.info(null, \"unable to find pwsh.exe, falling back to powershell.exe\")\n        return \"powershell.exe\"\n      })\n  })\n}\n\nexport async function getWindowsVm(debugLogger: DebugLogger): Promise<VmManager> {\n  const parallelsVmModule = await import(\"./ParallelsVm\")\n  let vmList: ParallelsVm[] = []\n  try {\n    vmList = (await parallelsVmModule.parseVmList(debugLogger)).filter(it => [\"win-10\", \"win-11\"].includes(it.os))\n  } catch (_error) {\n    if ((await isPwshAvailable.value) && (await isWineAvailable.value)) {\n      const vmModule = await import(\"./PwshVm\")\n      return new vmModule.PwshVmManager()\n    }\n  }\n  if (vmList.length === 0) {\n    throw new InvalidConfigurationError(\"Cannot find suitable Parallels Desktop virtual machine (Windows 10 is required) and cannot access `pwsh` and `wine` locally\")\n  }\n\n  // prefer running or suspended vm\n  return new parallelsVmModule.ParallelsVmManager(vmList.find(it => it.state === \"running\") || vmList.find(it => it.state === \"suspended\") || vmList[0])\n}\n\nconst isWineAvailable = new Lazy(async () => {\n  return isCommandAvailable(\"wine\", [\"--version\"])\n})\n\nexport const isPwshAvailable = new Lazy(async () => {\n  return isCommandAvailable(\"pwsh\", [\"--version\"])\n})\n\nexport const isCommandAvailable = async (command: string, args: string[]) => {\n  try {\n    await exec(command, args)\n    return true\n  } catch {\n    return false\n  }\n}\n"]}