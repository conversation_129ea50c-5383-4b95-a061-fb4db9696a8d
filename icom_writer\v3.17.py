# CS-F7500 UIA automation - v3.17
# - Kills any existing EX3852A.EXE before launching
# - Absolute-from-Home hops with HARD HOME
# - F2 for Name, Mode via dropdown (Enter, Down, Enter)
# - Detached launch + robust attach (PID or title)
# - ASCII-only to avoid Windows encoding weirdness

import os
import time
import csv
import subprocess
from datetime import datetime
from pywinauto import Desktop
from pywinauto.application import Application
from pywinauto import keyboard

# =========================
# CONFIG / PATHS
# =========================
WORKDIR = r"C:\cc\icom.py"
EXE_PATH = r"C:\Program Files (x86)\Icom\CS-F7500\EX3852A.EXE"

os.makedirs(WORKDIR, exist_ok=True)
CSV_FILE = os.path.join(WORKDIR, "new_channel.csv")
LOG_FILE = os.path.join(WORKDIR, "programming_log.txt")

WINDOW_TITLE_KEYS = ["CS-F7500", "Icom", "EX3852A"]

# Timeouts / waits (seconds)
POST_LAUNCH_WAIT   = 1.5
ATTACH_TIMEOUT     = 60.0
POST_ATTACH_SETTLE = 1.0
POST_MAXIMIZE_WAIT = 1.0
TREE_RETRY_SECONDS = 25

# Click inside the grid area to wake focus (adjust if needed)
GRID_NUDGE_POINT = (900, 400)

# Absolute offsets: RIGHT from Home to each column (your working hops)
HOME_TO_COL = {
    "name":  4,
    "mode":  5,
    "rx":    6,   # RX is required
    "tx":    7,
    "rxctc": 9,
    "txctc": 10,
}

# Mode dropdown: Enter, DOWN N, Enter (Analog = 1 down)
MODE_DROPDOWN_DOWN_PRESSES = 1

ALWAYS_SET_TONE_MODE = False
PAUSE_ON_ERROR = True
LOG_TO_FILE = True

# =========================
# LOGGING
# =========================
def log(msg: str):
    ts = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
    line = f"{ts} {msg}"
    print(line)
    if LOG_TO_FILE:
        try:
            with open(LOG_FILE, "a", encoding="utf-8") as f:
                f.write(line + "\n")
        except Exception:
            pass

def pause(label=""):
    if PAUSE_ON_ERROR:
        try:
            input(f"[{label}] Press Enter to continue...")
        except Exception:
            time.sleep(2)

# =========================
# CSV LOADING (enc fallback)
# =========================
def load_csv_with_fallbacks(path):
    encodings = ["utf-8-sig", "utf-8", "cp1252", "latin-1"]
    last_err = None
    for enc in encodings:
        try:
            with open(path, newline="", encoding=enc) as f:
                return list(csv.DictReader(f)), enc
        except Exception as e:
            last_err = e
    raise last_err

def parse_rows(dict_rows):
    out = []
    def get(r, *names):
        for n in names:
            v = r.get(n)
            if v is not None:
                return v.strip()
        return ""
    for r in dict_rows:
        name   = get(r, "name")
        rx     = get(r, "rx")
        tx     = get(r, "tx")
        rxtone = get(r, "rxtone", "rx_ctone")
        txtone = get(r, "txtone", "tx_ctone")
        out.append([rx, name, tx, rxtone, txtone])
    return out

# =========================
# PRELAUNCH CLEANUP
# =========================
def kill_existing_processes():
    names = ["EX3852A.EXE"]
    for name in names:
        try:
            r = subprocess.run(
                ["taskkill", "/F", "/IM", name],
                capture_output=True, text=True, shell=False
            )
            out = (r.stdout or "") + (r.stderr or "")
            if r.returncode == 0:
                log(f"Killed existing {name}")
            else:
                low = out.lower()
                if "no instance" in low or "not found" in low:
                    log(f"No running {name} to kill.")
                else:
                    log(f"WARN taskkill {name}: rc={r.returncode} msg='{out.strip()[:200]}'")
        except Exception as e:
            log(f"WARN taskkill {name}: {e}")
    time.sleep(0.8)

# =========================
# WINDOW DISCOVERY / ATTACH
# =========================
def list_top_windows():
    try:
        wins = Desktop(backend="uia").windows()
        for w in wins:
            try:
                log(f"WIN pid={w.process_id} vis={w.is_visible()} title='{w.window_text()}' class='{w.friendly_class_name()}'")
            except Exception:
                pass
    except Exception:
        pass

def try_connect_by_pid(pid):
    try:
        app = Application(backend="uia").connect(process=pid, timeout=5)
        win = app.top_window()
        return app, win
    except Exception:
        return None, None

def find_window_by_title_keys():
    wins = Desktop(backend="uia").windows()
    for w in wins:
        try:
            title = (w.window_text() or "").strip()
            if not title:
                continue
            for key in WINDOW_TITLE_KEYS:
                if key.lower() in title.lower() and w.is_visible():
                    return w
        except Exception:
            continue
    return None

def has_tree_control(win):
    try:
        t = win.child_window(control_type="Tree")
        return bool(t and t.exists())
    except Exception:
        return False

def attach_or_launch():
    log("Launching detached...")
    try:
        p = subprocess.Popen([EXE_PATH], shell=False)
        launch_pid = p.pid
        log(f"Launched PID={launch_pid}")
    except Exception as e:
        raise RuntimeError(f"launch failed: {e}")

    time.sleep(POST_LAUNCH_WAIT)

    t0 = time.time()
    app = None
    main = None
    last_dump = 0
    while time.time() - t0 < ATTACH_TIMEOUT:
        if app is None:
            app, win = try_connect_by_pid(launch_pid)
            if app and win:
                main = win
                log("Attached by PID to top_window()")

        if main is None:
            w = find_window_by_title_keys()
            if w:
                try:
                    app = Application(backend="uia").connect(handle=w.handle)
                    main = app.window(handle=w.handle)
                    log(f"Attached by title to '{main.window_text()}' (pid={w.process_id})")
                except Exception:
                    main = None

        if main:
            try:
                main.wait("visible", timeout=5)
                break
            except Exception:
                main = None

        if time.time() - last_dump > 5:
            log("Waiting for main window... listing top windows:")
            list_top_windows()
            last_dump = time.time()
        time.sleep(0.4)

    if not main:
        raise RuntimeError("attach_or_launch: could not find a running main window")

    try:
        main.maximize()
        time.sleep(POST_MAXIMIZE_WAIT)
    except Exception as e:
        log(f"WARN maximize: {e}")

    time.sleep(POST_ATTACH_SETTLE)
    return app, main

# =========================
# TREE SELECT
# =========================
def _expand(item):
    try:
        item.expand()
    except Exception:
        try:
            item.double_click_input()
        except Exception:
            pass
    time.sleep(0.15)

def _find_tree_root(main):
    try:
        t = main.child_window(control_type="Tree")
        if t.exists():
            return t
    except Exception:
        pass
    for el in main.descendants():
        try:
            if getattr(el.element_info, "class_name", "") == "TreeView":
                return el
        except Exception:
            continue
    return None

def _find_treeitem(root, label):
    want = label.strip().lower()
    for it in root.descendants(control_type="TreeItem"):
        nm = (it.window_text() or "").strip().lower()
        if nm == want:
            return it
    return None

def select_tree_items(main):
    deadline = time.time() + TREE_RETRY_SECONDS
    MEM, Z1A, Z1B = "Memory CH", "1: Zone 1", "Zone 1"
    while time.time() < deadline:
        root = _find_tree_root(main)
        if not root:
            keyboard.send_keys("{F6}")
            time.sleep(0.5)
            continue

        mem = _find_treeitem(root, MEM)
        if not mem:
            for ti in root.descendants(control_type="TreeItem"):
                _expand(ti)
            mem = _find_treeitem(root, MEM)
        if not mem:
            time.sleep(0.5)
            continue

        try:
            mem.click_input()
        except Exception:
            pass
        _expand(mem)

        zone = _find_treeitem(root, Z1A) or _find_treeitem(root, Z1B)
        if zone:
            try:
                zone.click_input()
            except Exception:
                try:
                    zone.double_click_input()
                except Exception:
                    pass
            log("Selected Memory CH -> Zone 1")
            return
        time.sleep(0.3)
    raise RuntimeError("Could not select 'Memory CH' -> 'Zone 1'")

# =========================
# GRID NAV HELPERS (ABSOLUTE + HARD HOME)
# =========================
def nudge_grid(main):
    try:
        main.click_input(coords=GRID_NUDGE_POINT)
        log("Nudged grid focus.")
    except Exception:
        pass
    time.sleep(0.25)

def ensure_nav_mode():
    # leave text-edit so Home/Arrows navigate cells
    keyboard.send_keys("{ESC}")
    time.sleep(0.06)

def hard_home_to_col0():
    # Aggressive homing to the leftmost cell in the current row.
    ensure_nav_mode()
    keyboard.send_keys("{HOME}")
    time.sleep(0.08)
    keyboard.send_keys("{HOME}")    # some grids need two Homes to go to column 0
    time.sleep(0.08)
    # fallbacks: Ctrl+Left shoves to the first column in many grids
    for _ in range(12):
        keyboard.send_keys("^({LEFT})")
        time.sleep(0.02)

def hop_from_home(rights: int):
    hard_home_to_col0()
    n = max(0, int(rights or 0))
    if n:
        keyboard.send_keys("{RIGHT %d}" % n)
    time.sleep(0.08)

def goto_col(key: str):
    offs = HOME_TO_COL.get(key)
    if offs is None:
        raise KeyError("Unknown column key: %s" % key)
    hop_from_home(offs)

def enter_value_here(text: str):
    # Generic numeric/text entry using Enter to start/commit
    keyboard.send_keys("{ENTER}")
    time.sleep(0.12)
    if text:
        keyboard.send_keys(str(text), with_spaces=True)
    keyboard.send_keys("{ENTER}")
    time.sleep(0.12)
    ensure_nav_mode()

def enter_text_cell(col_key: str, text: str, use_f2: bool = False):
    goto_col(col_key)
    if use_f2:
        keyboard.send_keys("{F2}")
        time.sleep(0.12)
    else:
        keyboard.send_keys("{ENTER}")
        time.sleep(0.12)
    if text:
        keyboard.send_keys(str(text), with_spaces=True)
    keyboard.send_keys("{ENTER}")
    time.sleep(0.12)
    ensure_nav_mode()

def pick_mode_dropdown():
    goto_col("mode")
    keyboard.send_keys("{ENTER}")
    time.sleep(0.12)
    d = max(0, MODE_DROPDOWN_DOWN_PRESSES)
    if d:
        keyboard.send_keys("{DOWN %d}" % d)
        time.sleep(0.08)
    keyboard.send_keys("{ENTER}")
    time.sleep(0.12)
    ensure_nav_mode()

def set_tone_user_then_freq(freq_text: str):
    keyboard.send_keys("{ENTER}")
    time.sleep(0.12)
    keyboard.send_keys("User{ENTER}")
    time.sleep(0.10)
    if freq_text:
        keyboard.send_keys(str(freq_text), with_spaces=True)
    keyboard.send_keys("{ENTER}")
    time.sleep(0.12)
    ensure_nav_mode()

# =========================
# MAIN
# =========================
def main():
    # Ensure correct working directory
    try:
        os.chdir(WORKDIR)
    except Exception:
        pass

    # Load CSV first (fail fast)
    try:
        dict_rows, used_enc = load_csv_with_fallbacks(CSV_FILE)
        log(f"Loaded CSV with encoding: {used_enc}")
    except Exception as e:
        log(f"ERROR reading CSV: {e}")
        pause("CSV")
        return
    rows = parse_rows(dict_rows)
    log(f"Rows: {len(rows)}")

    # Kill any pre-existing Icom process to avoid attaching to a zombie
    kill_existing_processes()

    # Launch and attach
    try:
        app, main = attach_or_launch()
    except Exception as e:
        log(f"ERROR attach/launch: {e}")
        log("Desktop windows at failure:")
        list_top_windows()
        pause("Attach")
        return

    # Wait until main UI has a Tree (means it's ready)
    t0 = time.time()
    while time.time() - t0 < 20:
        if has_tree_control(main):
            break
        time.sleep(0.5)

    # Select Memory CH -> Zone 1
    try:
        select_tree_items(main)
    except Exception as e:
        log(f"Tree selection failed: {e}")
        pause("Tree")
        return

    # Wake grid before any homing
    nudge_grid(main)

    # Per-row entry (RX required, then Name/Mode/TX/RXCTC/TXCTC)
    for i, (rx, name, tx, rxtone, txtone) in enumerate(rows, start=1):
        if not rx:
            log(f"Row {i}: SKIP (rx empty)")
            keyboard.send_keys("{DOWN}")
            time.sleep(0.05)
            continue

        log(f"Row {i}: rx={rx} name={name} tx={tx} rxtone={rxtone} txtone={txtone}")

        try:
            goto_col("rx");     enter_value_here(rx)
            enter_text_cell("name", name, use_f2=True)
            pick_mode_dropdown()
            goto_col("tx");     enter_value_here(tx)
            goto_col("rxctc")
            if rxtone or ALWAYS_SET_TONE_MODE:
                set_tone_user_then_freq(rxtone)
            else:
                enter_value_here("")
            goto_col("txctc")
            if txtone or ALWAYS_SET_TONE_MODE:
                set_tone_user_then_freq(txtone)
            else:
                enter_value_here("")
            keyboard.send_keys("{DOWN}")
            time.sleep(0.08)
        except Exception as e:
            log(f"Row {i} ERROR: {e}")
            pause(f"Row {i}")
            keyboard.send_keys("{DOWN}")
            time.sleep(0.06)

    log("Done (v3.17).")
    pause("End")

if __name__ == "__main__":
    main()
