// main.js
const {
  app,
  BrowserWindow,
  ipcMain,
  dialog,
  screen,
  Tray,
  Menu,
  nativeImage,
  Notification,
  shell,
} = require("electron");
const path = require("path");
const fs = require("fs");
const { spawn } = require("child_process");

const APP_ID = "com.nodekeeper.app";

const STATUS = {
  STARTING: "starting",
  RUNNING: "running",
  RESTARTING: "restarting",
  STOPPED: "stopped",
  CRASHED: "crashed",
  ERROR: "error",
};

let win;          // small status popover (hidden by default; tray toggles it)
let tray;         // system tray icon
let trayMenu;     // context menu
let child;        // npm start child
let status = STATUS.STOPPED;
let lastExit = null;
let consecutiveFailures = 0;
let backoffSeconds = 0;
let statusTimer;
let startedAt = null;

const userDir = app.getPath("userData");
const cfgPath = path.join(userDir, "monitorConfig.json");
const logPath = path.join(userDir, "restartLog.json");

// fallback 16x16 PNG so tray is NEVER invisible
const BASE64_TRAY =
  "iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAVklEQVR4nGNgGGjAiEtCbsL//+hijwoYMdQzEasZlziGAbg045JnwidJjCFM2ARJMQRrGJAChpMB2BIJPgBTz4RNkFjNGAYQYwi6PNYwwGUIqd4kCgAAeIEkGl4Wwk8AAAAASUVORK5CYII=";

// ---------- utils ----------
function readJSON(file, fallback) {
  try { return JSON.parse(fs.readFileSync(file, "utf-8")); } catch { return fallback; }
}
function writeJSON(file, data) {
  try { fs.writeFileSync(file, JSON.stringify(data, null, 2), "utf-8"); } catch {}
}
function getConfig() { return readJSON(cfgPath, { targetDir: null }); }
function setConfig(next) { writeJSON(cfgPath, next); }
function getRestartLog() { return readJSON(logPath, { timestamps: [] }); }
function pushRestartStamp() {
  const log = getRestartLog();
  log.timestamps.push(Date.now());
  const cutoff = Date.now() - 24 * 60 * 60 * 1000;
  log.timestamps = log.timestamps.filter((t) => t >= cutoff);
  writeJSON(logPath, log);
}
function restartsLast24h() {
  const cutoff = Date.now() - 24 * 60 * 60 * 1000;
  return getRestartLog().timestamps.filter((t) => t >= cutoff).length;
}
function calcBackoff() {
  const secs = Math.min(30, Math.pow(2, Math.max(0, consecutiveFailures - 1)));
  return Number.isFinite(secs) && secs > 0 ? secs : 1;
}
function pickNpmCommand() { return process.platform === "win32" ? "npm.cmd" : "npm"; }

// ---------- extra safety: kill-any-strays-before-spawn ----------
function execPowerShell(script) {
  return new Promise((resolve) => {
    const ps = spawn("powershell.exe",
      ["-NoProfile", "-ExecutionPolicy", "Bypass", "-Command", script],
      { windowsHide: true, stdio: "ignore" }
    );
    ps.on("exit", () => resolve());
    ps.on("error", () => resolve());
  });
}

/**
 * Ensures no lingering instance is running before (re)start.
 * 1) Kills our tracked child tree.
 * 2) On Windows, also force-kills any node/npm whose CommandLine mentions targetDir.
 */
async function preSpawnCleanup(targetDir) {
  // 1) kill our own child tree
  stopChild();
  // small grace to let OS finish termination
  await new Promise((r) => setTimeout(r, 300));

  // 2) Windows-wide sweep scoped to the project dir
  if (process.platform === "win32" && targetDir) {
    // Escape backslashes for PS string; use regex-escaped match inside PS
    const dirRaw = targetDir.replace(/"/g, '""');
    const script = `
$dir = "${dirRaw}";
$pattern = [regex]::Escape($dir);
Get-CimInstance Win32_Process |
  Where-Object {
    ($_.Name -match '^(node|npm)\\.exe$') -and
    ($_.CommandLine -match $pattern)
  } |
  ForEach-Object {
    try { Stop-Process -Id $_.ProcessId -Force -ErrorAction SilentlyContinue } catch {}
  }
`;
    await execPowerShell(script);
    // tiny delay after PS sweep
    await new Promise((r) => setTimeout(r, 200));
  }
}

// ---------- tray ----------
function getTrayIcon() {
  const assetsDir = path.join(__dirname, "assets");
  const png = path.join(assetsDir, "tray.png");
  const ico = path.join(assetsDir, "tray.ico");

  if (fs.existsSync(png)) {
    const img = nativeImage.createFromPath(png);
    if (!img.isEmpty()) return img;
  }
  if (fs.existsSync(ico)) {
    const img = nativeImage.createFromPath(ico);
    if (!img.isEmpty()) return img;
  }
  const img = nativeImage.createFromDataURL("data:image/png;base64," + BASE64_TRAY);
  return img.isEmpty() ? nativeImage.createFromBuffer(Buffer.from(BASE64_TRAY, "base64")) : img;
}

function ensureTray() {
  if (!tray) {
    tray = new Tray(getTrayIcon());
    tray.setToolTip("Node Keeper");
    tray.on("click", () => {
      if (win && !win.isDestroyed()) {
        win.isVisible() ? win.hide() : showStatusWindowNearTray();
      } else {
        createWindow(true);
        showStatusWindowNearTray();
      }
    });
  }
  updateTrayUI();
}

function updateTrayUI() {
  const s = getPublicStatus();
  const subtitle = `Status: ${s.status.toUpperCase()} • Restarts(24h): ${s.restarts24h}`;
  tray?.setToolTip(`Node Keeper\n${subtitle}`);

  const isRunning =
    s.status === STATUS.RUNNING ||
    s.status === STATUS.STARTING ||
    s.status === STATUS.RESTARTING;

  trayMenu = Menu.buildFromTemplate([
    { label: "Node Keeper", enabled: false },
    { type: "separator" },
    { label: `Status: ${s.status.toUpperCase()}`, enabled: false },
    { label: `Restarts (24h): ${s.restarts24h}`, enabled: false },
    { label: `Project: ${s.targetDir ?? "—"}`, enabled: false },
    { type: "separator" },
    { label: isRunning ? "Restart Now" : "Start", click: () => ipcStartOrRestart(isRunning) },
    { label: "Stop", enabled: isRunning, click: () => ipcStop() },
    { label: "Change Project…", click: async () => await ipcPickProject() },
    { type: "separator" },
    {
      label: "Show Status Window",
      click: () => {
        if (win && !win.isDestroyed()) { showStatusWindowNearTray(); }
        else { createWindow(true); showStatusWindowNearTray(); }
      },
    },
    { label: "Open Log Folder", click: () => shell.openPath(userDir) },
    { type: "separator" },
    { label: "Quit", role: "quit" },
  ]);

  tray?.setContextMenu(trayMenu);
}

function showStatusWindowNearTray() {
  if (!win || win.isDestroyed()) return;
  const { workArea } = screen.getPrimaryDisplay();

  // Adjust window size for mobile-like resolutions
  let WIDTH = 320, HEIGHT = 140;
  if (workArea.width <= 400) {
    WIDTH = Math.min(380, workArea.width - 20);
    HEIGHT = 180;
  }

  const x = Math.max(workArea.x + workArea.width - WIDTH - 12, workArea.x);
  const y = Math.max(workArea.y + workArea.height - HEIGHT - 12, workArea.y);

  win.setSize(WIDTH, HEIGHT);
  win.setPosition(x, y);
  win.show();
  win.focus();
}

// ---------- child (npm start) ----------
async function spawnNodeApp(targetDir) {
  if (!targetDir) {
    status = STATUS.ERROR;
    broadcast(); updateTrayUI();
    return;
  }

  // 🔒 ensure no lingering proc before starting
  await preSpawnCleanup(targetDir);

  status = status === STATUS.RESTARTING ? STATUS.RESTARTING : STATUS.STARTING;
  broadcast(); updateTrayUI();

  const cmd = pickNpmCommand();
  const opts = {
    cwd: targetDir,
    shell: process.platform === "win32",
    env: { ...process.env },
  };

  try {
    child = spawn(cmd, ["start"], opts);
  } catch (e) {
    status = STATUS.ERROR;
    lastExit = { code: null, signal: null, error: e.message };
    broadcast(); updateTrayUI();
    return;
  }

  startedAt = Date.now();

  child.on("spawn", () => { status = STATUS.RUNNING; broadcast(); updateTrayUI(); });
  child.on("error", (err) => {
    status = STATUS.ERROR;
    lastExit = { code: null, signal: null, error: String(err) };
    broadcast(); updateTrayUI();
  });

  child.on("exit", (code, signal) => {
    lastExit = { code, signal, error: null };
    const upTimeSec = startedAt ? (Date.now() - startedAt) / 1000 : 0;
    if (upTimeSec > 60) consecutiveFailures = 0; else consecutiveFailures++;

    pushRestartStamp();
    backoffSeconds = calcBackoff();
    status = STATUS.CRASHED;
    broadcast(); updateTrayUI();

    try {
      new Notification({
        title: "Node Keeper",
        body: `Process exited (code ${code ?? "?"}). Restarting in ${backoffSeconds}s…`,
      }).show();
    } catch {}

    scheduleRestart();
  });
}

function scheduleRestart() {
  status = STATUS.RESTARTING;
  broadcast(); updateTrayUI();

  setTimeout(() => {
    const { targetDir } = getConfig();
    // preSpawnCleanup is called inside spawnNodeApp too; double-safe is fine.
    spawnNodeApp(targetDir);
  }, backoffSeconds * 1000);
}

function stopChild() {
  if (child && !child.killed) {
    try {
      process.platform === "win32"
        ? spawn("taskkill", ["/pid", String(child.pid), "/T", "/F"], { windowsHide: true })
        : child.kill("SIGTERM");
    } catch {}
  }
  child = null;
  status = STATUS.STOPPED;
  broadcast(); updateTrayUI();
}

// ---------- popover window ----------
function createWindow(hideInitially = true) {
  if (win && !win.isDestroyed()) return;

  const primary = screen.getPrimaryDisplay().workArea;

  // Adjust window size for mobile-like resolutions
  let WIDTH = 320, HEIGHT = 140;
  if (primary.width <= 400) {
    WIDTH = Math.min(380, primary.width - 20);
    HEIGHT = 180;
  }

  const x = Math.max(primary.x + primary.width - WIDTH - 12, primary.x);
  const y = Math.max(primary.y + primary.height - HEIGHT - 12, primary.y);

  win = new BrowserWindow({
    width: WIDTH,
    height: HEIGHT,
    x, y,
    resizable: false,
    frame: false,
    alwaysOnTop: true,
    skipTaskbar: true,      // tray-first UX
    show: !hideInitially,   // hidden by default
    webPreferences: {
      preload: path.join(__dirname, "preload.js"),
      contextIsolation: true,
      nodeIntegration: false,
    },
  });

  win.loadFile(path.join(__dirname, "renderer", "index.html"));
  win.on("blur", () => { if (win && win.isVisible()) win.hide(); });
}

// ---------- status/IPC ----------
function getPublicStatus() {
  const { targetDir } = getConfig();
  return { status, restarts24h: restartsLast24h(), lastExit, backoffSeconds, targetDir };
}
function broadcast() {
  if (win && !win.isDestroyed()) win.webContents.send("status:update", getPublicStatus());
}

ipcMain.handle("status:get", () => getPublicStatus());

async function ensureConfig() {
  const cfg = getConfig();
  if (cfg.targetDir && fs.existsSync(path.join(cfg.targetDir, "package.json"))) return cfg.targetDir;

  const picked = await dialog.showOpenDialog({
    title: "Select the Node project folder (must contain package.json)",
    properties: ["openDirectory"],
  });
  if (picked.canceled || picked.filePaths.length === 0) return null;

  const dir = picked.filePaths[0];
  if (!fs.existsSync(path.join(dir, "package.json"))) {
    await dialog.showMessageBox({ type: "error", message: "That folder doesn’t contain a package.json. Pick the project root." });
    return null;
  }
  setConfig({ targetDir: dir });
  return dir;
}

async function ipcPickProject() {
  const dir = await ensureConfig();
  if (dir) {
    consecutiveFailures = 0;
    backoffSeconds = 0;
    await spawnNodeApp(dir); // includes preSpawnCleanup
  }
  updateTrayUI();
  return getPublicStatus();
}
function ipcStop() { stopChild(); return getPublicStatus(); }
async function ipcStartOrRestart(isRunning) {
  const { targetDir } = getConfig();
  if (!targetDir) return getPublicStatus();
  consecutiveFailures = 0;
  backoffSeconds = 0;
  if (isRunning) stopChild();
  await spawnNodeApp(targetDir); // includes preSpawnCleanup
  return getPublicStatus();
}

ipcMain.handle("action:pickAppDir", ipcPickProject);
ipcMain.handle("action:stop", ipcStop);
ipcMain.handle("action:start", () => ipcStartOrRestart(false));
ipcMain.handle("action:openLogFolder", () => { shell.openPath(userDir); return true; });

// ---------- app lifecycle ----------
app.whenReady().then(async () => {
  if (process.platform === "win32") app.setAppUserModelId(APP_ID);

  if (!fs.existsSync(userDir)) fs.mkdirSync(userDir, { recursive: true });
  if (!fs.existsSync(logPath)) writeJSON(logPath, { timestamps: [] });

  ensureTray();
  createWindow(true);

  const dir = await ensureConfig();
  if (dir) { await spawnNodeApp(dir); }
  else {
    status = STATUS.ERROR;
    lastExit = { code: null, signal: null, error: "No valid project selected." };
    broadcast(); updateTrayUI();
  }

  statusTimer = setInterval(() => {
    restartsLast24h();
    broadcast(); updateTrayUI();
  }, 2000);
});

// keep running in tray even if window closes
app.on("window-all-closed", () => {});
app.on("before-quit", () => { try { clearInterval(statusTimer); } catch {} stopChild(); });
